#!/usr/bin/env python3
"""
Minescript 4.0 Mining Automation Script
======================================

This script provides automated mining with movement along the X-axis while continuously attacking.
Features:
- Continuous attack while moving back and forth between X coordinates -26 and 29
- Tkinter GUI with Start, Stop, and Emergency Stop controls
- Safety system that checks for minecraft:iron_bars underneath the player
- Graceful error handling and status feedback

Usage: \mining_automation
"""

import minescript
import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
import sys
import traceback


class MiningAutomation:
    def __init__(self):
        # Automation state
        self.running = False
        self.emergency_stop = False
        self.direction = 1  # 1 for positive X, -1 for negative X
        self.automation_thread = None

        # Boundaries
        self.min_x = -26
        self.max_x = 29

        # Safety settings
        self.safety_block = "minecraft:iron_bars"
        self.safety_check_interval = 0.5  # Check every 500ms

        # Performance settings
        self.movement_delay = 0.05  # Delay between movement checks
        self.transition_delay = 0.1  # Delay during direction changes

        # Error handling settings
        self.max_position_errors = 10
        self.stuck_threshold = 5.0  # seconds before considering player stuck
        self.stuck_movement_threshold = 0.1  # minimum movement to not be considered stuck

        # GUI components
        self.root = None
        self.status_var = None
        self.position_var = None

        # Runtime statistics
        self.start_time = None
        self.direction_changes = 0

        # Initialize GUI
        self.setup_gui()
    
    def setup_gui(self):
        """Initialize the Tkinter GUI"""
        self.root = tk.Tk()
        self.root.title("Minescript Mining Automation")
        self.root.geometry("400x300")
        self.root.resizable(False, False)
        
        # Configure style
        style = ttk.Style()
        style.theme_use('clam')
        
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Title
        title_label = ttk.Label(main_frame, text="Mining Automation Control", 
                               font=('Arial', 14, 'bold'))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # Status section
        status_frame = ttk.LabelFrame(main_frame, text="Status", padding="10")
        status_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.status_var = tk.StringVar(value="Ready")
        status_label = ttk.Label(status_frame, textvariable=self.status_var)
        status_label.grid(row=0, column=0, sticky=tk.W)
        
        self.position_var = tk.StringVar(value="Position: Not available")
        position_label = ttk.Label(status_frame, textvariable=self.position_var)
        position_label.grid(row=1, column=0, sticky=tk.W)

        # Statistics display
        self.stats_var = tk.StringVar(value="Runtime: 0s | Direction changes: 0")
        stats_label = ttk.Label(status_frame, textvariable=self.stats_var)
        stats_label.grid(row=2, column=0, sticky=tk.W)
        
        # Control buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=2, column=0, columnspan=2, pady=10)
        
        self.start_button = ttk.Button(button_frame, text="Start Mining", 
                                      command=self.start_automation, width=15)
        self.start_button.grid(row=0, column=0, padx=(0, 10))
        
        self.stop_button = ttk.Button(button_frame, text="Stop Mining", 
                                     command=self.stop_automation, width=15, 
                                     state=tk.DISABLED)
        self.stop_button.grid(row=0, column=1, padx=(0, 10))
        
        self.emergency_button = ttk.Button(button_frame, text="EMERGENCY STOP", 
                                          command=self.emergency_stop_automation, 
                                          width=15)
        self.emergency_button.grid(row=0, column=2)
        
        # Configure emergency button style
        style.configure('Emergency.TButton', foreground='white', background='red')
        self.emergency_button.configure(style='Emergency.TButton')
        
        # Settings section
        settings_frame = ttk.LabelFrame(main_frame, text="Settings", padding="10")
        settings_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=10)
        
        ttk.Label(settings_frame, text=f"X Boundaries: {self.min_x} to {self.max_x}").grid(row=0, column=0, sticky=tk.W)
        ttk.Label(settings_frame, text=f"Safety Block: {self.safety_block}").grid(row=1, column=0, sticky=tk.W)
        
        # Info section
        info_frame = ttk.LabelFrame(main_frame, text="Information", padding="10")
        info_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=10)
        
        info_text = ("This script will move the player back and forth along the X-axis\n"
                    "while continuously holding the attack button. The safety system\n"
                    "will stop the script if the block underneath is not iron bars.")
        ttk.Label(info_frame, text=info_text, justify=tk.LEFT).grid(row=0, column=0, sticky=tk.W)
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        
        # Start position update timer
        self.update_position_display()
    
    def update_position_display(self):
        """Update the position display and statistics in the GUI"""
        try:
            if not self.emergency_stop:
                pos = minescript.player_position()
                self.position_var.set(f"Position: X={pos[0]:.1f}, Y={pos[1]:.1f}, Z={pos[2]:.1f}")

                # Update statistics if running
                if self.running and self.start_time:
                    runtime = int(time.time() - self.start_time)
                    self.stats_var.set(f"Runtime: {runtime}s | Direction changes: {self.direction_changes}")
                else:
                    self.stats_var.set(f"Runtime: 0s | Direction changes: {self.direction_changes}")
        except Exception as e:
            self.position_var.set(f"Position: Error - {str(e)}")

        # Schedule next update
        self.root.after(1000, self.update_position_display)
    
    def check_safety_condition(self):
        """Check if the block underneath the player is iron_bars"""
        try:
            pos = minescript.player_position()
            # Check block directly underneath player
            block_below = minescript.getblock(int(pos[0]), int(pos[1]) - 1, int(pos[2]))
            return block_below == self.safety_block
        except Exception as e:
            minescript.echo(f"Safety check error: {e}")
            return False
    
    def start_automation(self):
        """Start the mining automation"""
        if self.running:
            return
        
        # Initial safety check
        if not self.check_safety_condition():
            messagebox.showerror("Safety Check Failed", 
                               f"The block underneath the player is not {self.safety_block}.\n"
                               "Please move to a safe location before starting.")
            return
        
        self.running = True
        self.emergency_stop = False

        # Initialize statistics
        self.start_time = time.time()
        self.direction_changes = 0

        # Update GUI
        self.start_button.configure(state=tk.DISABLED)
        self.stop_button.configure(state=tk.NORMAL)
        self.status_var.set("Starting automation...")

        # Start automation thread
        self.automation_thread = threading.Thread(target=self.automation_loop, daemon=True)
        self.automation_thread.start()

        minescript.echo("Mining automation started!")
    
    def stop_automation(self):
        """Stop the mining automation gracefully"""
        if not self.running:
            return
        
        self.running = False
        self.status_var.set("Stopping...")
        
        # Wait for thread to finish
        if self.automation_thread and self.automation_thread.is_alive():
            self.automation_thread.join(timeout=2.0)
        
        self.cleanup_automation()
        minescript.echo("Mining automation stopped.")
    
    def emergency_stop_automation(self):
        """Emergency stop - immediately halt all operations"""
        self.emergency_stop = True
        self.running = False
        
        self.cleanup_automation()
        self.status_var.set("EMERGENCY STOPPED")
        
        minescript.echo("EMERGENCY STOP ACTIVATED!")
        messagebox.showwarning("Emergency Stop", "All automation has been immediately halted!")
    
    def cleanup_automation(self):
        """Clean up automation state and GUI"""
        # Stop all player actions
        try:
            minescript.player_press_attack(False)
            minescript.player_press_forward(False)
            minescript.player_press_backward(False)
        except Exception as e:
            minescript.echo(f"Cleanup error: {e}")
        
        # Update GUI
        self.start_button.configure(state=tk.NORMAL)
        self.stop_button.configure(state=tk.DISABLED)
        
        if not self.emergency_stop:
            self.status_var.set("Ready")
    
    def automation_loop(self):
        """Main automation loop with enhanced error handling"""
        position_error_count = 0
        last_position = None
        stuck_check_time = time.time()

        try:
            self.status_var.set("Running - Mining active")

            # Start attacking
            minescript.player_press_attack(True)

            last_safety_check = time.time()

            while self.running and not self.emergency_stop:
                # Periodic safety check
                current_time = time.time()
                if current_time - last_safety_check >= self.safety_check_interval:
                    if not self.check_safety_condition():
                        self.status_var.set("SAFETY VIOLATION - Stopping")
                        minescript.echo("SAFETY VIOLATION: Block underneath is not iron_bars!")
                        self.emergency_stop_automation()
                        return
                    last_safety_check = current_time

                # Get current position with error handling
                try:
                    pos = minescript.player_position()
                    current_x = pos[0]
                    position_error_count = 0  # Reset error count on success

                    # Check if player is stuck (hasn't moved significantly)
                    if last_position is not None:
                        distance_moved = abs(current_x - last_position[0])
                        if distance_moved < self.stuck_movement_threshold:
                            if current_time - stuck_check_time > self.stuck_threshold:
                                minescript.echo("Player appears to be stuck! Attempting to unstuck...")
                                self.attempt_unstuck()
                                stuck_check_time = current_time
                        else:
                            stuck_check_time = current_time  # Reset stuck timer

                    last_position = pos

                except Exception as e:
                    position_error_count += 1
                    minescript.echo(f"Position error ({position_error_count}/{self.max_position_errors}): {e}")

                    if position_error_count >= self.max_position_errors:
                        minescript.echo("Too many position errors - stopping automation")
                        self.status_var.set("Connection issues - Stopped")
                        self.emergency_stop_automation()
                        return

                    time.sleep(0.5)  # Longer delay on error
                    continue

                # Validate position is reasonable
                if abs(current_x) > 1000:  # Sanity check for reasonable coordinates
                    minescript.echo(f"Warning: Player at unusual position X={current_x}")

                # Check boundaries and adjust direction
                if current_x >= self.max_x and self.direction == 1:
                    # Reached max X, reverse to negative direction
                    self.direction = -1
                    self.direction_changes += 1
                    minescript.player_press_forward(False)
                    time.sleep(self.transition_delay)  # Brief pause for smooth transition
                    minescript.player_press_backward(True)
                    self.status_var.set(f"Running - Moving backward (X: {current_x:.1f})")
                    minescript.echo(f"Reached max boundary ({self.max_x}), reversing direction")

                elif current_x <= self.min_x and self.direction == -1:
                    # Reached min X, reverse to positive direction
                    self.direction = 1
                    self.direction_changes += 1
                    minescript.player_press_backward(False)
                    time.sleep(self.transition_delay)  # Brief pause for smooth transition
                    minescript.player_press_forward(True)
                    self.status_var.set(f"Running - Moving forward (X: {current_x:.1f})")
                    minescript.echo(f"Reached min boundary ({self.min_x}), reversing direction")

                else:
                    # Continue in current direction
                    if self.direction == 1:
                        minescript.player_press_forward(True)
                        minescript.player_press_backward(False)
                        self.status_var.set(f"Running - Moving forward (X: {current_x:.1f})")
                    else:
                        minescript.player_press_backward(True)
                        minescript.player_press_forward(False)
                        self.status_var.set(f"Running - Moving backward (X: {current_x:.1f})")

                # Small delay to prevent excessive CPU usage
                time.sleep(self.movement_delay)

        except Exception as e:
            error_msg = f"Automation error: {e}"
            minescript.echo(error_msg)
            minescript.echo(f"Traceback: {traceback.format_exc()}")
            self.status_var.set("Error occurred - Stopped")
            self.emergency_stop_automation()

        finally:
            self.cleanup_automation()

    def attempt_unstuck(self):
        """Attempt to unstuck the player by jumping and brief direction changes"""
        try:
            # Try jumping
            minescript.player_press_jump(True)
            time.sleep(0.2)
            minescript.player_press_jump(False)

            # Brief opposite direction movement
            if self.direction == 1:
                minescript.player_press_forward(False)
                minescript.player_press_backward(True)
                time.sleep(0.3)
                minescript.player_press_backward(False)
            else:
                minescript.player_press_backward(False)
                minescript.player_press_forward(True)
                time.sleep(0.3)
                minescript.player_press_forward(False)

        except Exception as e:
            minescript.echo(f"Error during unstuck attempt: {e}")
    
    def run(self):
        """Start the GUI main loop"""
        try:
            minescript.echo("Mining Automation GUI started. Use the interface to control the automation.")
            self.root.mainloop()
        except KeyboardInterrupt:
            self.emergency_stop_automation()
        except Exception as e:
            minescript.echo(f"GUI error: {e}")
            self.emergency_stop_automation()


def main():
    """Main entry point"""
    try:
        # Create and run the automation
        automation = MiningAutomation()
        automation.run()
    except Exception as e:
        minescript.echo(f"Failed to start mining automation: {e}")
        minescript.echo(f"Traceback: {traceback.format_exc()}")


if __name__ == "__main__":
    main()
