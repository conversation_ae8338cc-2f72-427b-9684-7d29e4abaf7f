import minescript
import time

# Helper function to press the right mouse button to hold
def hold_right_click(pressed):
    minescript.player_press_use(pressed)

# Helper function to check if the targeted block is a fully grown carrot plant
def is_fully_grown_carrot():
    targeted_block = minescript.player_get_targeted_block(max_distance=5)
    if targeted_block is not None:
        # Check if the targeted block is a carrot plant and fully grown (age 7)
        block_type = targeted_block.type
        block_properties = getattr(targeted_block, 'properties', None)

        if block_type == 'minecraft:carrots' and block_properties and block_properties.get('age') == '7':
            return True
    return False

# Helper function to break the targeted block
def break_targeted_block():
    minescript.player_press_attack(True)
    time.sleep(0.1)
    minescript.player_press_attack(False)

def main():
    while True:
        # Hold right-click to use the current item (planting mode)
        hold_right_click(True)
        time.sleep(0.1)  # small delay to simulate holding the button

        # Check if the targeted block is a fully grown carrot plant
        if is_fully_grown_carrot():
            hold_right_click(False)  # Release right-click before breaking
            break_targeted_block()

        # Short delay before the next loop iteration
        time.sleep(0.5)

if __name__ == "__main__":
    main()
