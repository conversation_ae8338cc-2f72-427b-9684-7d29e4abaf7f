#!/usr/bin/env python3
"""
Debug Block Detection Script
===========================

This script helps debug block detection issues in the mining automation.
It provides detailed information about block detection and coordinate conversion.

Usage: \debug_block_detection
"""

import minescript
import math


def extract_base_block_name(block_string):
    """
    Extract the base block name from a full Minecraft block string.
    Same function as in mining_automation.py for consistency.
    """
    if not block_string:
        return block_string

    bracket_index = block_string.find('[')

    if bracket_index == -1:
        return block_string
    else:
        return block_string[:bracket_index]


def debug_player_position():
    """Debug player position and coordinate conversion"""
    minescript.echo("=== Player Position Debug ===")
    
    try:
        pos = minescript.player_position()
        player_x, player_y, player_z = pos
        
        minescript.echo(f"Raw player position: X={player_x}, Y={player_y}, Z={player_z}")
        
        # Test different coordinate conversion methods
        minescript.echo("\nCoordinate conversion methods:")
        
        # Method 1: Simple int() conversion (truncation)
        int_x = int(player_x)
        int_y = int(player_y)
        int_z = int(player_z)
        minescript.echo(f"int() conversion: X={int_x}, Y={int_y}, Z={int_z}")
        
        # Method 2: math.floor() (proper block alignment)
        floor_x = math.floor(player_x)
        floor_y = math.floor(player_y)
        floor_z = math.floor(player_z)
        minescript.echo(f"math.floor(): X={floor_x}, Y={floor_y}, Z={floor_z}")
        
        # Method 3: round() (nearest block)
        round_x = round(player_x)
        round_y = round(player_y)
        round_z = round(player_z)
        minescript.echo(f"round(): X={round_x}, Y={round_y}, Z={round_z}")
        
        return pos
        
    except Exception as e:
        minescript.echo(f"Error getting player position: {e}")
        return None


def test_block_detection_methods(pos):
    """Test different methods of detecting the block underneath"""
    if not pos:
        return
        
    minescript.echo("\n=== Block Detection Methods ===")
    
    player_x, player_y, player_z = pos
    
    methods = [
        ("int() - 1", int(player_x), int(player_y) - 1, int(player_z)),
        ("floor() - 1", math.floor(player_x), math.floor(player_y) - 1, math.floor(player_z)),
        ("round() - 1", round(player_x), round(player_y) - 1, round(player_z)),
        ("floor() at feet", math.floor(player_x), math.floor(player_y), math.floor(player_z)),
    ]
    
    for method_name, bx, by, bz in methods:
        try:
            block_full = minescript.getblock(bx, by, bz)
            block_base = extract_base_block_name(block_full)
            minescript.echo(f"{method_name:15} [{bx:3}, {by:3}, {bz:3}]: {block_base}")
            if block_full != block_base:
                minescript.echo(f"{'':15}   Full: {block_full}")
        except Exception as e:
            minescript.echo(f"{method_name:15} [{bx:3}, {by:3}, {bz:3}]: ERROR - {e}")


def scan_area_around_player(pos, radius=2):
    """Scan blocks in an area around the player"""
    if not pos:
        return
        
    minescript.echo(f"\n=== Area Scan (radius {radius}) ===")
    
    player_x, player_y, player_z = pos
    center_x = math.floor(player_x)
    center_y = math.floor(player_y)
    center_z = math.floor(player_z)
    
    minescript.echo(f"Scanning around block position: [{center_x}, {center_y}, {center_z}]")
    
    # Scan horizontal plane at player's feet level
    minescript.echo(f"\nHorizontal scan at Y={center_y}:")
    for dz in range(-radius, radius + 1):
        line = ""
        for dx in range(-radius, radius + 1):
            try:
                block_full = minescript.getblock(center_x + dx, center_y, center_z + dz)
                block_base = extract_base_block_name(block_full)
                # Abbreviate block names for display
                if "deepslate_bricks" in block_base:
                    symbol = "D"
                elif "iron_bars" in block_base:
                    symbol = "I"
                elif "air" in block_base:
                    symbol = "."
                elif "stone" in block_base:
                    symbol = "S"
                elif "dirt" in block_base:
                    symbol = "d"
                else:
                    symbol = "?"
                line += f"{symbol:2}"
            except:
                line += " E"
        minescript.echo(f"  Z{center_z + dz:+3}: {line}")
    
    # Scan vertical column below player
    minescript.echo(f"\nVertical scan at X={center_x}, Z={center_z}:")
    for dy in range(2, -5, -1):  # From 2 blocks above to 4 blocks below
        y_pos = center_y + dy
        try:
            block_full = minescript.getblock(center_x, y_pos, center_z)
            block_base = extract_base_block_name(block_full)
            marker = " <-- Player level" if dy == 0 else " <-- Below player" if dy == -1 else ""
            minescript.echo(f"  Y{y_pos:3}: {block_base}{marker}")
            if block_full != block_base and dy == -1:  # Show full string for block below player
                minescript.echo(f"    Full: {block_full}")
        except Exception as e:
            minescript.echo(f"  Y{y_pos:3}: ERROR - {e}")


def test_deepslate_bricks_detection():
    """Specifically test for deepslate bricks detection"""
    minescript.echo("\n=== Deepslate Bricks Detection Test ===")

    try:
        pos = minescript.player_position()
        player_x, player_y, player_z = pos

        # Use the same method as the mining automation
        block_x = math.floor(player_x)
        block_y = math.floor(player_y) - 1
        block_z = math.floor(player_z)

        minescript.echo(f"Testing position: [{block_x}, {block_y}, {block_z}]")

        block_full = minescript.getblock(block_x, block_y, block_z)
        block_base = extract_base_block_name(block_full)

        minescript.echo(f"Full detected block: '{block_full}'")
        minescript.echo(f"Base block name: '{block_base}'")

        # Test various deepslate bricks variations
        deepslate_bricks_variants = [
            "minecraft:deepslate_bricks",
            "deepslate_bricks",
            "minecraft:deepslate_brick",
            "deepslate_brick"
        ]

        minescript.echo("\nTesting base block against deepslate bricks variants:")
        for variant in deepslate_bricks_variants:
            match = (block_base == variant)
            minescript.echo(f"  '{variant}': {match}")

        # Check if it contains deepslate_bricks
        contains_deepslate = "deepslate_bricks" in block_base.lower()
        minescript.echo(f"\nBase block contains 'deepslate_bricks': {contains_deepslate}")

        # Show block state analysis
        if '[' in block_full:
            state_part = block_full[block_full.find('['):]
            minescript.echo(f"Block state properties: {state_part}")
        else:
            minescript.echo("No block state properties detected")

        return block_base

    except Exception as e:
        minescript.echo(f"Error in deepslate bricks test: {e}")
        return None


def comprehensive_debug():
    """Run comprehensive debugging"""
    minescript.echo("Starting comprehensive block detection debug...")
    minescript.echo("=" * 60)
    
    # Get player position
    pos = debug_player_position()
    
    # Test different detection methods
    test_block_detection_methods(pos)
    
    # Scan area around player
    scan_area_around_player(pos)
    
    # Test deepslate bricks specifically
    detected_block = test_deepslate_bricks_detection()

    minescript.echo("\n" + "=" * 60)
    minescript.echo("Debug complete!")

    if detected_block:
        if "deepslate_bricks" in detected_block:
            minescript.echo("✓ Deepslate bricks detected - automation should work")
            minescript.echo("  The safety system will now properly handle block states")
        else:
            minescript.echo(f"⚠ Different block detected: {detected_block}")
            minescript.echo("  Move to deepslate bricks before running automation")


def main():
    """Main entry point"""
    try:
        comprehensive_debug()
    except Exception as e:
        minescript.echo(f"Debug script error: {e}")
        import traceback
        minescript.echo(f"Traceback: {traceback.format_exc()}")


if __name__ == "__main__":
    main()
