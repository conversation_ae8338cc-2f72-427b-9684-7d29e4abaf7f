#!/usr/bin/env python3
"""
Debug Block Detection Script
===========================

This script helps debug block detection issues in the mining automation.
It provides detailed information about block detection and coordinate conversion.

Usage: \debug_block_detection
"""

import minescript
import math


def debug_player_position():
    """Debug player position and coordinate conversion"""
    minescript.echo("=== Player Position Debug ===")
    
    try:
        pos = minescript.player_position()
        player_x, player_y, player_z = pos
        
        minescript.echo(f"Raw player position: X={player_x}, Y={player_y}, Z={player_z}")
        
        # Test different coordinate conversion methods
        minescript.echo("\nCoordinate conversion methods:")
        
        # Method 1: Simple int() conversion (truncation)
        int_x = int(player_x)
        int_y = int(player_y)
        int_z = int(player_z)
        minescript.echo(f"int() conversion: X={int_x}, Y={int_y}, Z={int_z}")
        
        # Method 2: math.floor() (proper block alignment)
        floor_x = math.floor(player_x)
        floor_y = math.floor(player_y)
        floor_z = math.floor(player_z)
        minescript.echo(f"math.floor(): X={floor_x}, Y={floor_y}, Z={floor_z}")
        
        # Method 3: round() (nearest block)
        round_x = round(player_x)
        round_y = round(player_y)
        round_z = round(player_z)
        minescript.echo(f"round(): X={round_x}, Y={round_y}, Z={round_z}")
        
        return pos
        
    except Exception as e:
        minescript.echo(f"Error getting player position: {e}")
        return None


def test_block_detection_methods(pos):
    """Test different methods of detecting the block underneath"""
    if not pos:
        return
        
    minescript.echo("\n=== Block Detection Methods ===")
    
    player_x, player_y, player_z = pos
    
    methods = [
        ("int() - 1", int(player_x), int(player_y) - 1, int(player_z)),
        ("floor() - 1", math.floor(player_x), math.floor(player_y) - 1, math.floor(player_z)),
        ("round() - 1", round(player_x), round(player_y) - 1, round(player_z)),
        ("floor() at feet", math.floor(player_x), math.floor(player_y), math.floor(player_z)),
    ]
    
    for method_name, bx, by, bz in methods:
        try:
            block = minescript.getblock(bx, by, bz)
            minescript.echo(f"{method_name:15} [{bx:3}, {by:3}, {bz:3}]: {block}")
        except Exception as e:
            minescript.echo(f"{method_name:15} [{bx:3}, {by:3}, {bz:3}]: ERROR - {e}")


def scan_area_around_player(pos, radius=2):
    """Scan blocks in an area around the player"""
    if not pos:
        return
        
    minescript.echo(f"\n=== Area Scan (radius {radius}) ===")
    
    player_x, player_y, player_z = pos
    center_x = math.floor(player_x)
    center_y = math.floor(player_y)
    center_z = math.floor(player_z)
    
    minescript.echo(f"Scanning around block position: [{center_x}, {center_y}, {center_z}]")
    
    # Scan horizontal plane at player's feet level
    minescript.echo(f"\nHorizontal scan at Y={center_y}:")
    for dz in range(-radius, radius + 1):
        line = ""
        for dx in range(-radius, radius + 1):
            try:
                block = minescript.getblock(center_x + dx, center_y, center_z + dz)
                # Abbreviate block names for display
                if "iron_bars" in block:
                    symbol = "I"
                elif "air" in block:
                    symbol = "."
                elif "stone" in block:
                    symbol = "S"
                elif "dirt" in block:
                    symbol = "D"
                else:
                    symbol = "?"
                line += f"{symbol:2}"
            except:
                line += " E"
        minescript.echo(f"  Z{center_z + dz:+3}: {line}")
    
    # Scan vertical column below player
    minescript.echo(f"\nVertical scan at X={center_x}, Z={center_z}:")
    for dy in range(2, -5, -1):  # From 2 blocks above to 4 blocks below
        y_pos = center_y + dy
        try:
            block = minescript.getblock(center_x, y_pos, center_z)
            marker = " <-- Player level" if dy == 0 else " <-- Below player" if dy == -1 else ""
            minescript.echo(f"  Y{y_pos:3}: {block}{marker}")
        except Exception as e:
            minescript.echo(f"  Y{y_pos:3}: ERROR - {e}")


def test_iron_bars_detection():
    """Specifically test for iron bars detection"""
    minescript.echo("\n=== Iron Bars Detection Test ===")
    
    try:
        pos = minescript.player_position()
        player_x, player_y, player_z = pos
        
        # Use the same method as the mining automation
        block_x = math.floor(player_x)
        block_y = math.floor(player_y) - 1
        block_z = math.floor(player_z)
        
        minescript.echo(f"Testing position: [{block_x}, {block_y}, {block_z}]")
        
        block = minescript.getblock(block_x, block_y, block_z)
        minescript.echo(f"Detected block: '{block}'")
        
        # Test various iron bars variations
        iron_bars_variants = [
            "minecraft:iron_bars",
            "iron_bars",
            "minecraft:iron_bar",
            "iron_bar"
        ]
        
        minescript.echo("\nTesting against iron bars variants:")
        for variant in iron_bars_variants:
            match = (block == variant)
            minescript.echo(f"  '{variant}': {match}")
        
        # Check if it contains iron_bars
        contains_iron = "iron_bars" in block.lower()
        minescript.echo(f"\nContains 'iron_bars': {contains_iron}")
        
        return block
        
    except Exception as e:
        minescript.echo(f"Error in iron bars test: {e}")
        return None


def comprehensive_debug():
    """Run comprehensive debugging"""
    minescript.echo("Starting comprehensive block detection debug...")
    minescript.echo("=" * 60)
    
    # Get player position
    pos = debug_player_position()
    
    # Test different detection methods
    test_block_detection_methods(pos)
    
    # Scan area around player
    scan_area_around_player(pos)
    
    # Test iron bars specifically
    detected_block = test_iron_bars_detection()
    
    minescript.echo("\n" + "=" * 60)
    minescript.echo("Debug complete!")
    
    if detected_block:
        if "iron_bars" in detected_block:
            minescript.echo("✓ Iron bars detected - automation should work")
        else:
            minescript.echo(f"⚠ Different block detected: {detected_block}")
            minescript.echo("  Move to iron bars before running automation")


def main():
    """Main entry point"""
    try:
        comprehensive_debug()
    except Exception as e:
        minescript.echo(f"Debug script error: {e}")
        import traceback
        minescript.echo(f"Traceback: {traceback.format_exc()}")


if __name__ == "__main__":
    main()
