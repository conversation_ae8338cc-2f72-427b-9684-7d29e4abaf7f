import minescript
import math

def calculate_distance(x1, y1, z1, x2, y2, z2):
    """Calculate the Euclidean distance between two points (x1, y1, z1) and (x2, y2, z2), rounding the result."""
    return round(math.sqrt((x2 - x1)**2 + (y2 - y1)**2 + (z2 - z1)**2))

def echo_player_info():
    # Get the list of nearby players
    player_list = minescript.players()  # Retrieve players using the correct method
    
    # Get your own position
    my_position = minescript.player_position()  # Use the correct method to get your coordinates

    if player_list:
        for player in player_list:
            # Retrieve player information
            player_name = player.name  # Player's name
            player_health = round(player.health)  # Round the player's health
            player_position = [round(coord) for coord in player.position]  # Round player position (x, y, z)

            # Round your position as well
            my_position_rounded = [round(coord) for coord in my_position]

            # Calculate distance between you and the player
            distance = calculate_distance(my_position_rounded[0], my_position_rounded[1], my_position_rounded[2], player_position[0], player_position[1], player_position[2])

            # Echo player information and rounded distance
            minescript.echo(f"Player: {player_name}")
            minescript.echo(f"Health: {player_health}")
            minescript.echo(f"Position: X={player_position[0]}, Y={player_position[1]}, Z={player_position[2]}")
            minescript.echo(f"Distance from you: {distance} blocks")
            minescript.echo("----------------------------")
    else:
        minescript.echo("No players found.")

def main():
    # Call the function to echo player info
    echo_player_info()

if __name__ == "__main__":
    main()
