# CustomTkinter Installation Guide

The updated mining automation script now uses CustomTkinter for a modern, professional GUI appearance. Follow these steps to install CustomTkinter.

## Installation Methods

### Method 1: Using pip (Recommended)

Open a command prompt or terminal and run:

```bash
pip install customtkinter
```

### Method 2: Using pip with specific Python version

If you have multiple Python versions:

```bash
python -m pip install customtkinter
# or
python3 -m pip install customtkinter
```

### Method 3: For Conda users

```bash
conda install -c conda-forge customtkinter
```

## Verification

To verify CustomTkinter is installed correctly, run this test:

```python
import customtkinter as ctk

# Create a simple test window
root = ctk.CTk()
root.title("CustomTkinter Test")
root.geometry("300x200")

label = ctk.CTkLabel(root, text="CustomTkinter is working!")
label.pack(pady=20)

button = ctk.CTkButton(root, text="Close", command=root.quit)
button.pack(pady=10)

root.mainloop()
```

## Troubleshooting

### Common Issues

**ImportError: No module named 'customtkinter'**
- Make sure you're using the correct Python environment
- Try reinstalling: `pip uninstall customtkinter` then `pip install customtkinter`

**GUI appears but looks wrong**
- Update to the latest version: `pip install --upgrade customtkinter`
- Check your system's display scaling settings

**Performance issues**
- CustomTkinter requires a modern graphics system
- On older systems, you can fall back to the original tkinter version

## Features of the Updated GUI

The new CustomTkinter interface includes:

### Visual Improvements
- **Dark theme** by default with modern styling
- **Rounded buttons** with hover effects
- **Professional color scheme** with blue accents
- **Better typography** with custom fonts
- **Improved spacing** and layout

### Enhanced Controls
- **Colored emergency stop button** (red with hover effects)
- **Disabled state styling** for better visual feedback
- **Modern frame styling** with subtle borders
- **Consistent button sizing** and alignment

### Theme Customization

You can customize the appearance by modifying these lines in the script:

```python
# Change appearance mode
ctk.set_appearance_mode("light")  # "dark" or "light"

# Change color theme
ctk.set_default_color_theme("green")  # "blue", "green", "dark-blue"
```

## Fallback to Standard Tkinter

If you cannot install CustomTkinter, you can revert to the original tkinter version by:

1. Changing the import:
   ```python
   # From:
   import customtkinter as ctk
   
   # To:
   import tkinter as tk
   from tkinter import ttk
   ```

2. Replacing CustomTkinter components with tkinter equivalents:
   - `ctk.CTk()` → `tk.Tk()`
   - `ctk.CTkFrame()` → `ttk.Frame()`
   - `ctk.CTkLabel()` → `ttk.Label()`
   - `ctk.CTkButton()` → `ttk.Button()`

However, the modern styling and improved appearance will be lost.

## System Requirements

CustomTkinter works on:
- **Windows 10/11** (recommended)
- **macOS 10.14+**
- **Linux** with modern desktop environments

Minimum Python version: **Python 3.7+**

## Additional Resources

- [CustomTkinter Documentation](https://customtkinter.tomschimansky.com/)
- [CustomTkinter GitHub](https://github.com/TomSchimansky/CustomTkinter)
- [Examples and Tutorials](https://github.com/TomSchimansky/CustomTkinter/tree/master/examples)

## Support

If you encounter issues with CustomTkinter installation:

1. **Check Python version**: `python --version`
2. **Update pip**: `pip install --upgrade pip`
3. **Try virtual environment**:
   ```bash
   python -m venv mining_env
   # Windows:
   mining_env\Scripts\activate
   # Linux/Mac:
   source mining_env/bin/activate
   pip install customtkinter
   ```

4. **Check for conflicts**: Uninstall and reinstall if needed
5. **Use the debug script** to test basic functionality before running the main automation
