#!/usr/bin/env python3
"""
Test script for Mining Automation
=================================

This script provides basic tests and utilities for the mining automation system.
Use this to verify that the Minescript API is working correctly before running
the full automation.

Usage: \test_mining
"""

import minescript
import time


def extract_base_block_name(block_string):
    """
    Extract the base block name from a full Minecraft block string.
    Same function as in mining_automation.py for consistency.
    """
    if not block_string:
        return block_string

    bracket_index = block_string.find('[')

    if bracket_index == -1:
        return block_string
    else:
        return block_string[:bracket_index]


def test_basic_functions():
    """Test basic Minescript functions"""
    minescript.echo("=== Testing Basic Functions ===")
    
    try:
        # Test player position
        pos = minescript.player_position()
        minescript.echo(f"Player position: X={pos[0]:.2f}, Y={pos[1]:.2f}, Z={pos[2]:.2f}")
        
        # Test block detection
        block_below = minescript.getblock(int(pos[0]), int(pos[1]) - 1, int(pos[2]))
        minescript.echo(f"Block below player: {block_below}")
        
        # Test player info
        player_info = minescript.player()
        minescript.echo(f"Player name: {player_info.name}")
        
        minescript.echo("✓ Basic functions working correctly")
        return True
        
    except Exception as e:
        minescript.echo(f"✗ Error in basic functions: {e}")
        return False


def test_movement_controls():
    """Test movement control functions"""
    minescript.echo("=== Testing Movement Controls ===")
    
    try:
        # Test brief forward movement
        minescript.echo("Testing forward movement...")
        minescript.player_press_forward(True)
        time.sleep(0.5)
        minescript.player_press_forward(False)
        
        time.sleep(0.5)
        
        # Test brief backward movement
        minescript.echo("Testing backward movement...")
        minescript.player_press_backward(True)
        time.sleep(0.5)
        minescript.player_press_backward(False)
        
        # Test attack
        minescript.echo("Testing attack...")
        minescript.player_press_attack(True)
        time.sleep(0.5)
        minescript.player_press_attack(False)
        
        minescript.echo("✓ Movement controls working correctly")
        return True
        
    except Exception as e:
        minescript.echo(f"✗ Error in movement controls: {e}")
        return False


def test_safety_check():
    """Test the safety check for iron bars"""
    minescript.echo("=== Testing Safety Check ===")

    try:
        import math
        pos = minescript.player_position()

        # Use the same method as the updated mining automation
        player_x, player_y, player_z = pos
        block_x = math.floor(player_x)
        block_y = math.floor(player_y) - 1  # Block underneath
        block_z = math.floor(player_z)

        minescript.echo(f"Player position: X={player_x:.3f}, Y={player_y:.3f}, Z={player_z:.3f}")
        minescript.echo(f"Checking block at: X={block_x}, Y={block_y}, Z={block_z}")

        block_below_full = minescript.getblock(block_x, block_y, block_z)
        block_below_base = extract_base_block_name(block_below_full)
        expected_block = "minecraft:deepslate_bricks"

        minescript.echo(f"Full block string: '{block_below_full}'")
        minescript.echo(f"Base block name: '{block_below_base}'")
        minescript.echo(f"Expected block: '{expected_block}'")

        if block_below_base == expected_block:
            minescript.echo("✓ Safety check PASSED - deepslate_bricks detected")
            return True
        else:
            minescript.echo("⚠ Safety check FAILED - deepslate_bricks NOT detected")
            minescript.echo("Please move to a location with deepslate_bricks underneath before running automation")
            minescript.echo("Run \\debug_block_detection for detailed analysis")
            return False

    except Exception as e:
        minescript.echo(f"✗ Error in safety check: {e}")
        return False


def test_boundary_positions():
    """Test if player can reach the boundary positions"""
    minescript.echo("=== Testing Boundary Positions ===")
    
    try:
        pos = minescript.player_position()
        current_x = pos[0]
        
        min_x = -26
        max_x = 29
        
        minescript.echo(f"Current X position: {current_x:.2f}")
        minescript.echo(f"Target boundaries: {min_x} to {max_x}")
        
        if min_x <= current_x <= max_x:
            minescript.echo("✓ Player is within target boundaries")
        else:
            minescript.echo("⚠ Player is outside target boundaries")
            
        distance_to_min = abs(current_x - min_x)
        distance_to_max = abs(current_x - max_x)
        
        minescript.echo(f"Distance to min boundary ({min_x}): {distance_to_min:.2f}")
        minescript.echo(f"Distance to max boundary ({max_x}): {distance_to_max:.2f}")
        
        return True
        
    except Exception as e:
        minescript.echo(f"✗ Error in boundary test: {e}")
        return False


def run_all_tests():
    """Run all tests"""
    minescript.echo("Starting Mining Automation Tests...")
    minescript.echo("=" * 50)
    
    tests = [
        ("Basic Functions", test_basic_functions),
        ("Movement Controls", test_movement_controls),
        ("Safety Check", test_safety_check),
        ("Boundary Positions", test_boundary_positions)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        minescript.echo(f"\nRunning {test_name}...")
        if test_func():
            passed += 1
        time.sleep(1)  # Brief pause between tests
    
    minescript.echo("\n" + "=" * 50)
    minescript.echo(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        minescript.echo("✓ All tests passed! Mining automation should work correctly.")
    else:
        minescript.echo("⚠ Some tests failed. Please address issues before running automation.")
    
    return passed == total


def main():
    """Main entry point"""
    try:
        run_all_tests()
    except Exception as e:
        minescript.echo(f"Test script error: {e}")


if __name__ == "__main__":
    main()
