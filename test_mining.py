#!/usr/bin/env python3
"""
Test script for Mining Automation
=================================

This script provides basic tests and utilities for the mining automation system.
Use this to verify that the Minescript API is working correctly before running
the full automation.

Usage: \test_mining
"""

import minescript
import time


def test_basic_functions():
    """Test basic Minescript functions"""
    minescript.echo("=== Testing Basic Functions ===")
    
    try:
        # Test player position
        pos = minescript.player_position()
        minescript.echo(f"Player position: X={pos[0]:.2f}, Y={pos[1]:.2f}, Z={pos[2]:.2f}")
        
        # Test block detection
        block_below = minescript.getblock(int(pos[0]), int(pos[1]) - 1, int(pos[2]))
        minescript.echo(f"Block below player: {block_below}")
        
        # Test player info
        player_info = minescript.player()
        minescript.echo(f"Player name: {player_info.name}")
        
        minescript.echo("✓ Basic functions working correctly")
        return True
        
    except Exception as e:
        minescript.echo(f"✗ Error in basic functions: {e}")
        return False


def test_movement_controls():
    """Test movement control functions"""
    minescript.echo("=== Testing Movement Controls ===")
    
    try:
        # Test brief forward movement
        minescript.echo("Testing forward movement...")
        minescript.player_press_forward(True)
        time.sleep(0.5)
        minescript.player_press_forward(False)
        
        time.sleep(0.5)
        
        # Test brief backward movement
        minescript.echo("Testing backward movement...")
        minescript.player_press_backward(True)
        time.sleep(0.5)
        minescript.player_press_backward(False)
        
        # Test attack
        minescript.echo("Testing attack...")
        minescript.player_press_attack(True)
        time.sleep(0.5)
        minescript.player_press_attack(False)
        
        minescript.echo("✓ Movement controls working correctly")
        return True
        
    except Exception as e:
        minescript.echo(f"✗ Error in movement controls: {e}")
        return False


def test_safety_check():
    """Test the safety check for iron bars"""
    minescript.echo("=== Testing Safety Check ===")
    
    try:
        pos = minescript.player_position()
        block_below = minescript.getblock(int(pos[0]), int(pos[1]) - 1, int(pos[2]))
        
        minescript.echo(f"Block below player: {block_below}")
        
        if block_below == "minecraft:iron_bars":
            minescript.echo("✓ Safety check PASSED - iron_bars detected")
            return True
        else:
            minescript.echo("⚠ Safety check FAILED - iron_bars NOT detected")
            minescript.echo("Please move to a location with iron_bars underneath before running automation")
            return False
            
    except Exception as e:
        minescript.echo(f"✗ Error in safety check: {e}")
        return False


def test_boundary_positions():
    """Test if player can reach the boundary positions"""
    minescript.echo("=== Testing Boundary Positions ===")
    
    try:
        pos = minescript.player_position()
        current_x = pos[0]
        
        min_x = -26
        max_x = 29
        
        minescript.echo(f"Current X position: {current_x:.2f}")
        minescript.echo(f"Target boundaries: {min_x} to {max_x}")
        
        if min_x <= current_x <= max_x:
            minescript.echo("✓ Player is within target boundaries")
        else:
            minescript.echo("⚠ Player is outside target boundaries")
            
        distance_to_min = abs(current_x - min_x)
        distance_to_max = abs(current_x - max_x)
        
        minescript.echo(f"Distance to min boundary ({min_x}): {distance_to_min:.2f}")
        minescript.echo(f"Distance to max boundary ({max_x}): {distance_to_max:.2f}")
        
        return True
        
    except Exception as e:
        minescript.echo(f"✗ Error in boundary test: {e}")
        return False


def run_all_tests():
    """Run all tests"""
    minescript.echo("Starting Mining Automation Tests...")
    minescript.echo("=" * 50)
    
    tests = [
        ("Basic Functions", test_basic_functions),
        ("Movement Controls", test_movement_controls),
        ("Safety Check", test_safety_check),
        ("Boundary Positions", test_boundary_positions)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        minescript.echo(f"\nRunning {test_name}...")
        if test_func():
            passed += 1
        time.sleep(1)  # Brief pause between tests
    
    minescript.echo("\n" + "=" * 50)
    minescript.echo(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        minescript.echo("✓ All tests passed! Mining automation should work correctly.")
    else:
        minescript.echo("⚠ Some tests failed. Please address issues before running automation.")
    
    return passed == total


def main():
    """Main entry point"""
    try:
        run_all_tests()
    except Exception as e:
        minescript.echo(f"Test script error: {e}")


if __name__ == "__main__":
    main()
