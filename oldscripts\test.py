import minescript
import time
import sys

def hold_leftclick():
    minescript.player_press_attack(True)

def unhold_leftclick():
    minescript.player_press_attack(False)

def move_right_until_trapdoorSOUTH():
    minescript.player_press_right(True)
    while True:
        current_position = minescript.player_position()
        current_x2 = current_position[0] - 1
        current_y2 = current_position[1] - 1
        current_z2 = current_position[2] - 1
        
        block_below = minescript.getblock(current_x2, current_y2, current_z2)
        minescript.echo(f"Block below at ({current_x2}, {current_y2}, {current_z2}): {block_below}")
        if block_below == 'minecraft:acacia_trapdoor[facing=east,half=top,open=false,powered=false,waterlogged=false]':
            break
    minescript.player_press_right(False)

def move_left_until_trapdoorSOUTH():
    minescript.player_press_left(True)
    while True:
        current_position = minescript.player_position()
        current_x3 = current_position[0]
        current_y3 = current_position[1] - 1
        current_z3 = current_position[2] - 1
        
        block_below = minescript.getblock(current_x3, current_y3, current_z3)
        
        if block_below == 'minecraft:acacia_trapdoor[facing=west,half=top,open=false,powered=false,waterlogged=false]':
            break
    minescript.player_press_left(False)

def move_right_until_trapdoorSOUTH():
    minescript.player_press_right(True)
    while True:
        current_position = minescript.player_position()
        current_x2 = current_position[0] - 1
        current_y2 = current_position[1] - 1
        current_z2 = current_position[2] - 1
        
        block_below = minescript.getblock(current_x2, current_y2, current_z2)
        
        if block_below == 'minecraft:acacia_trapdoor[facing=east,half=top,open=false,powered=false,waterlogged=false]':
            break
    minescript.player_press_right(False)

def move_left_until_trapdoorNORTH():
    minescript.player_press_left(True)
    while True:
        current_position = minescript.player_position()
        current_x3 = current_position[0] - 1
        current_y3 = current_position[1] - 1
        current_z3 = current_position[2] - 1
        
        block_below = minescript.getblock(current_x3, current_y3, current_z3)
        #minescript.echo(f"Block below at ({current_x3}, {current_y3}, {current_z3}): {block_below}")
        if block_below == 'minecraft:acacia_trapdoor[facing=east,half=top,open=false,powered=false,waterlogged=false]':
            break
    minescript.player_press_left(False)

def move_right_until_trapdoorNORTH():
    minescript.player_press_right(True)
    while True:
        current_position = minescript.player_position()
        current_x3 = current_position[0] + 1
        current_y3 = current_position[1] - 1
        current_z3 = current_position[2] - 1
        
        block_below = minescript.getblock(current_x3, current_y3, current_z3)
        
        if block_below == 'minecraft:acacia_trapdoor[facing=west,half=top,open=false,powered=false,waterlogged=false]':
            break
    minescript.player_press_right(False)

def go_up():
    minescript.player_press_jump(True)
    time.sleep(0.6)
    minescript.player_press_jump(False)

def look(horizontal, vertical):
    minescript.player_set_orientation(horizontal, vertical)

def layerNORTH():
    move_right_until_trapdoorNORTH()
    go_up()
    move_left_until_trapdoorNORTH()
    go_up()

def layerSOUTH():
    move_right_until_trapdoorSOUTH()
    go_up()
    move_left_until_trapdoorSOUTH()
    go_up()

def check_side():
    current_position1 = minescript.player_position()
    target_x1 = current_position1[0] - 2
    target_y1 = current_position1[1] + 1
    target_z1 = current_position1[2] - 1
    block_type = minescript.getblock(target_x1, target_y1, target_z1)

    if block_type == 'minecraft:white_concrete':
        look(-180, 5)
        layerNORTH()
    elif block_type == 'minecraft:air':
        look(0, 5)
        layerSOUTH()


def run():
    current_position = minescript.player_position()
    target_x = current_position[0]
    target_y = current_position[1] + 4
    target_z = current_position[2] + 1
    block_type = minescript.getblock(target_x, target_y, target_z)

    if block_type == 'minecraft:farmland[moisture=7]':
        hold_leftclick()
        check_side()
    elif block_type == 'minecraft:air':
        unhold_leftclick()
        return

while True:
    run()