#!/usr/bin/env python3
"""
GUI Resize and Scroll Testing Script
===================================

This script tests the resizable and scrollable functionality of the mining
automation GUI without requiring Minescript to be running.

Usage: python test_gui_resize.py (run outside of Minecraft)
"""

try:
    import customtkinter as ctk
    from tkinter import messagebox
    import threading
    import time
except ImportError as e:
    print(f"Error importing required modules: {e}")
    print("Please install CustomTkinter: pip install customtkinter")
    exit(1)


class MockMiningAutomation:
    """Mock version of MiningAutomation for testing GUI functionality"""
    
    def __init__(self):
        # Mock automation state
        self.running = False
        self.emergency_stop = False
        self.direction = 1
        self.automation_thread = None
        
        # Mock boundaries and settings
        self.min_x = -26
        self.max_x = 29
        self.safety_block = "minecraft:iron_bars"
        self.safety_check_interval = 0.5
        
        # Mock statistics
        self.start_time = None
        self.direction_changes = 0
        
        # GUI components
        self.root = None
        self.status_var = None
        self.position_var = None
        self.stats_var = None
        self.scrollable_frame = None
        
        # Initialize GUI
        self.setup_gui()
    
    def setup_gui(self):
        """Initialize the CustomTkinter GUI with resizable and scrollable features"""
        # Set CustomTkinter appearance mode and color theme
        ctk.set_appearance_mode("dark")
        ctk.set_default_color_theme("blue")
        
        self.root = ctk.CTk()
        self.root.title("Mining Automation GUI Test - Resizable & Scrollable")
        
        # Set default size and make window resizable
        self.root.geometry("500x600")
        self.root.minsize(400, 400)
        self.root.resizable(True, True)
        
        # Configure grid weight for responsive layout
        self.root.grid_columnconfigure(0, weight=1)
        self.root.grid_rowconfigure(0, weight=1)
        
        # Create main scrollable frame
        self.scrollable_frame = ctk.CTkScrollableFrame(
            self.root,
            label_text="Mining Automation Control (Test Mode)",
            label_font=ctk.CTkFont(size=18, weight="bold")
        )
        self.scrollable_frame.grid(row=0, column=0, sticky="nsew", padx=20, pady=20)
        self.scrollable_frame.grid_columnconfigure(0, weight=1)
        
        # Status section
        status_frame = ctk.CTkFrame(self.scrollable_frame)
        status_frame.grid(row=0, column=0, sticky="ew", pady=(10, 15), padx=10)
        status_frame.grid_columnconfigure(0, weight=1)
        
        status_title = ctk.CTkLabel(status_frame, text="Status", 
                                   font=ctk.CTkFont(size=14, weight="bold"))
        status_title.grid(row=0, column=0, pady=(15, 5))
        
        self.status_var = ctk.StringVar(value="Test Mode - Ready")
        status_label = ctk.CTkLabel(status_frame, textvariable=self.status_var)
        status_label.grid(row=1, column=0, pady=3)
        
        self.position_var = ctk.StringVar(value="Position: X=0.0, Y=64.0, Z=0.0 (Mock)")
        position_label = ctk.CTkLabel(status_frame, textvariable=self.position_var)
        position_label.grid(row=2, column=0, pady=3)
        
        self.stats_var = ctk.StringVar(value="Runtime: 0s | Direction changes: 0")
        stats_label = ctk.CTkLabel(status_frame, textvariable=self.stats_var)
        stats_label.grid(row=3, column=0, pady=(3, 15))
        
        # Control buttons section
        button_frame = ctk.CTkFrame(self.scrollable_frame)
        button_frame.grid(row=1, column=0, sticky="ew", pady=(0, 15), padx=10)
        button_frame.grid_columnconfigure((0, 1, 2), weight=1)
        
        button_title = ctk.CTkLabel(button_frame, text="Controls", 
                                   font=ctk.CTkFont(size=14, weight="bold"))
        button_title.grid(row=0, column=0, columnspan=3, pady=(15, 10))
        
        self.start_button = ctk.CTkButton(button_frame, text="Start Mining (Test)", 
                                         command=self.mock_start_automation, 
                                         font=ctk.CTkFont(weight="bold"))
        self.start_button.grid(row=1, column=0, padx=(15, 5), pady=(0, 15), sticky="ew")
        
        self.stop_button = ctk.CTkButton(button_frame, text="Stop Mining", 
                                        command=self.mock_stop_automation,
                                        state="disabled",
                                        font=ctk.CTkFont(weight="bold"))
        self.stop_button.grid(row=1, column=1, padx=5, pady=(0, 15), sticky="ew")
        
        self.emergency_button = ctk.CTkButton(button_frame, text="EMERGENCY STOP", 
                                             command=self.mock_emergency_stop, 
                                             fg_color="#dc2626", hover_color="#b91c1c",
                                             font=ctk.CTkFont(weight="bold"))
        self.emergency_button.grid(row=1, column=2, padx=(5, 15), pady=(0, 15), sticky="ew")
        
        # Settings section
        settings_frame = ctk.CTkFrame(self.scrollable_frame)
        settings_frame.grid(row=2, column=0, sticky="ew", pady=(0, 15), padx=10)
        settings_frame.grid_columnconfigure(0, weight=1)
        
        settings_title = ctk.CTkLabel(settings_frame, text="Settings", 
                                     font=ctk.CTkFont(size=14, weight="bold"))
        settings_title.grid(row=0, column=0, pady=(15, 5))
        
        boundaries_label = ctk.CTkLabel(settings_frame, text=f"X Boundaries: {self.min_x} to {self.max_x}")
        boundaries_label.grid(row=1, column=0, pady=3)
        
        safety_label = ctk.CTkLabel(settings_frame, text=f"Safety Block: {self.safety_block}")
        safety_label.grid(row=2, column=0, pady=(3, 15))
        
        # Info section
        info_frame = ctk.CTkFrame(self.scrollable_frame)
        info_frame.grid(row=3, column=0, sticky="ew", pady=(0, 15), padx=10)
        info_frame.grid_columnconfigure(0, weight=1)
        
        info_title = ctk.CTkLabel(info_frame, text="Information", 
                                 font=ctk.CTkFont(size=14, weight="bold"))
        info_title.grid(row=0, column=0, pady=(15, 5))
        
        info_text = ("This is a test version of the mining automation GUI.\n"
                    "It demonstrates the resizable and scrollable functionality\n"
                    "without requiring Minescript to be running.")
        info_label = ctk.CTkLabel(info_frame, text=info_text, justify="left")
        info_label.grid(row=1, column=0, pady=(3, 15))
        
        # Test section to demonstrate scrolling
        test_frame = ctk.CTkFrame(self.scrollable_frame)
        test_frame.grid(row=4, column=0, sticky="ew", pady=(0, 15), padx=10)
        test_frame.grid_columnconfigure(0, weight=1)
        
        test_title = ctk.CTkLabel(test_frame, text="GUI Testing Features", 
                                 font=ctk.CTkFont(size=14, weight="bold"))
        test_title.grid(row=0, column=0, pady=(15, 5))
        
        test_text = ("✓ Window is resizable - try dragging the corners\n"
                    "✓ Content scrolls vertically with mouse wheel\n"
                    "✓ Buttons scale with window width\n"
                    "✓ Minimum window size enforced (400x400)\n"
                    "✓ All sections maintain proper spacing\n"
                    "✓ Status updates work in real-time\n"
                    "✓ Emergency stop button has red styling\n"
                    "✓ Dark theme with modern appearance")
        test_label = ctk.CTkLabel(test_frame, text=test_text, justify="left")
        test_label.grid(row=1, column=0, pady=(3, 15))
        
        # Resize instructions
        resize_frame = ctk.CTkFrame(self.scrollable_frame)
        resize_frame.grid(row=5, column=0, sticky="ew", pady=(0, 20), padx=10)
        resize_frame.grid_columnconfigure(0, weight=1)
        
        resize_title = ctk.CTkLabel(resize_frame, text="Resize Testing Instructions", 
                                   font=ctk.CTkFont(size=14, weight="bold"))
        resize_title.grid(row=0, column=0, pady=(15, 5))
        
        resize_text = ("1. Drag window corners to resize\n"
                      "2. Try making window very small (minimum 400x400)\n"
                      "3. Try making window very large\n"
                      "4. Use mouse wheel to scroll up and down\n"
                      "5. Test button functionality at different sizes\n"
                      "6. Observe how content scales responsively")
        resize_label = ctk.CTkLabel(resize_frame, text=resize_text, justify="left")
        resize_label.grid(row=1, column=0, pady=(3, 15))
        
        # Bind window resize event
        self.root.bind("<Configure>", self.on_window_resize)
        
        # Start mock position updates
        self.update_mock_display()
    
    def on_window_resize(self, event):
        """Handle window resize events"""
        if event.widget == self.root:
            width = self.root.winfo_width()
            height = self.root.winfo_height()
            
            if width < 400:
                self.root.geometry(f"400x{height}")
            if height < 400:
                self.root.geometry(f"{width}x400")
    
    def update_mock_display(self):
        """Update mock position and status display"""
        import random
        
        # Mock position updates
        mock_x = random.uniform(-26, 29)
        mock_y = random.uniform(60, 70)
        mock_z = random.uniform(-10, 10)
        
        self.position_var.set(f"Position: X={mock_x:.1f}, Y={mock_y:.1f}, Z={mock_z:.1f} (Mock)")
        
        # Mock statistics
        if self.running and self.start_time:
            runtime = int(time.time() - self.start_time)
            self.stats_var.set(f"Runtime: {runtime}s | Direction changes: {self.direction_changes}")
        
        # Schedule next update
        self.root.after(1000, self.update_mock_display)
    
    def mock_start_automation(self):
        """Mock start automation"""
        self.running = True
        self.start_time = time.time()
        self.start_button.configure(state="disabled")
        self.stop_button.configure(state="normal")
        self.status_var.set("Test Mode - Running (Mock)")
        print("Mock automation started")
    
    def mock_stop_automation(self):
        """Mock stop automation"""
        self.running = False
        self.start_button.configure(state="normal")
        self.stop_button.configure(state="disabled")
        self.status_var.set("Test Mode - Stopped")
        print("Mock automation stopped")
    
    def mock_emergency_stop(self):
        """Mock emergency stop"""
        self.running = False
        self.emergency_stop = True
        self.start_button.configure(state="normal")
        self.stop_button.configure(state="disabled")
        self.status_var.set("Test Mode - Emergency Stopped")
        messagebox.showwarning("Emergency Stop", "Mock emergency stop activated!")
        print("Mock emergency stop activated")
    
    def run(self):
        """Start the GUI main loop"""
        print("Starting GUI resize and scroll test...")
        print("Try resizing the window and scrolling through content!")
        self.root.mainloop()


def main():
    """Main entry point"""
    try:
        app = MockMiningAutomation()
        app.run()
    except Exception as e:
        print(f"Error running GUI test: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
