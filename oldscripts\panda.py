import minescript
import time
import http.client
import json
from datetime import datetime

# Define the webhook URL
DISCORD_WEBHOOK_URL = "https://discord.com/api/webhooks/1284442609741467658/Vj8ffFhGBNm8A0q7I4jYSnt9xuXADMYIfbW9M4K73QCX3NtdEnwoawzyLAYhOxKIh-KW"

# Define role ID for ping
ROLE_ID = "1284454672312303637"

def send_discord_message(content):
    """Send a message to Discord via webhook."""
    try:
        conn = http.client.HTTPSConnection("discord.com")
        headers = {'Content-Type': 'application/json'}
        body = json.dumps({'content': content})
        
        # Send the request to the webhook
        conn.request("POST", DISCORD_WEBHOOK_URL, body, headers)
        response = conn.getresponse()
        response_body = response.read().decode()
        
        # Check for successful response
        if response.status != 204:
            minescript.echo(f"Failed to send message: {response.status} {response.reason}")
            minescript.echo(f"Response body: {response_body}")
    except Exception as e:
        minescript.echo(f"Exception occurred: {str(e)}")
    finally:
        conn.close()

def on_chat_message(message):
    """Check chat messages for specific items and send a ping to Discord."""
    if "won" in message and "and 4 other rewards from the" in message and "PANDA CRATE" in message:
        # Check for specific items in the message
        if any(item in message for item in ["Panda Boots", "Panda Chestplate", "Panda Helmet"]):
            # Send message with ping for specific items
            discord_message = f"**{message}** <@&{ROLE_ID}>"
            send_discord_message(discord_message)
        else:
            # Send message without ping for other items
            send_discord_message(message)

def main():
    """Continuously monitor chat for updates and process messages."""
    with minescript.EventQueue() as event_queue:
        # Register chat listener to capture chat messages
        event_queue.register_chat_listener()

        while True:
            # Get the next event
            event = event_queue.get()

            # Process chat events
            if event.type == minescript.EventType.CHAT:
                # Handle the chat message
                on_chat_message(event.message)

            # Sleep to avoid excessive processing (adjust as necessary)
            time.sleep(1)

if __name__ == "__main__":
    main()
