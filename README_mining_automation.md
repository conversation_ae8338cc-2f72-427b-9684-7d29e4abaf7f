# Minescript 4.0 Mining Automation

A comprehensive automation script for Minecraft using Minescript 4.0 that provides automated mining with movement along the X-axis while continuously attacking.

## Features

### Core Functionality
- **Continuous Attack**: Maintains attack state throughout the entire operation
- **Automated Movement**: Moves player back and forth along X-axis between coordinates -26 and 29
- **Boundary Detection**: Automatically reverses direction when reaching boundaries
- **Smooth Transitions**: Includes brief pauses during direction changes for smooth movement

### GUI Interface
- **Modern CustomTkinter GUI**: Professional dark-themed interface with modern styling
- **Resizable Window**: Fully resizable interface that adapts to different screen sizes
- **Scrollable Content**: Vertical scrolling support for all content sections
- **Responsive Layout**: Buttons and content scale appropriately with window size
- **Start/Stop Controls**: Easy-to-use buttons with hover effects and visual feedback
- **Emergency Stop**: Prominent red emergency button for immediate safety halt
- **Real-time Status**: Live updates of automation status and player position
- **Statistics Display**: Shows runtime and direction change count
- **Enhanced Visual Design**: Rounded buttons, better typography, and improved spacing
- **Flexible Sizing**: Default 500x600, minimum 400x400, expandable to any size

### Safety System
- **Iron Bars Detection**: Continuously checks that the block underneath is `minecraft:iron_bars`
- **Block State Handling**: Properly handles Minecraft block states (ignores connection properties)
- **Pre-start Validation**: Prevents script execution if safety conditions aren't met
- **Runtime Monitoring**: Triggers emergency stop if safety block changes during operation
- **Configurable Check Interval**: Safety checks every 500ms by default

### Error Handling
- **Connection Monitoring**: Detects and handles player disconnection
- **Stuck Detection**: Identifies when player isn't moving and attempts to unstuck
- **Position Error Recovery**: Handles temporary position reading failures
- **Graceful Degradation**: Continues operation despite minor errors

## Installation

1. **Install CustomTkinter** (required for modern GUI):
   ```bash
   pip install customtkinter
   ```
   See `INSTALL_CUSTOMTKINTER.md` for detailed installation instructions.

2. **Ensure Minescript 4.0** is installed and working

3. **Copy the script files** to your `minecraft/minescript/` directory:
   - `mining_automation.py` - Main automation script with resizable GUI and block state handling
   - `test_mining.py` - Test script for verification
   - `debug_block_detection.py` - Debug utility for block detection issues
   - `test_block_states.py` - Comprehensive block state testing utility
   - `test_gui_resize.py` - GUI resize and scroll testing (standalone)
   - `README_mining_automation.md` - This documentation
   - `INSTALL_CUSTOMTKINTER.md` - CustomTkinter installation guide

## Usage

### Pre-flight Testing
Before running the main automation, test your setup:

```
\test_mining
```

This will verify:
- Basic Minescript functions are working
- Movement controls respond correctly
- Safety conditions are met (with improved coordinate detection)
- Player is within target boundaries

If you encounter block detection issues, run the debug utilities:

```
\debug_block_detection
\test_block_states
```

To test the GUI functionality outside of Minecraft:

```
python test_gui_resize.py
```

These provide detailed analysis of:
- Player position and coordinate conversion
- Block detection methods comparison
- Area scanning around the player
- Iron bars detection testing
- Block state property handling
- Safety system compatibility testing
- GUI resizing and scrolling functionality

### Running the Automation

1. **Position yourself** on iron bars within the X coordinate range (-26 to 29)
2. **Run the script**: `\mining_automation`
3. **Use the GUI** to control the automation:
   - Click "Start Mining" to begin
   - Click "Stop Mining" for graceful shutdown
   - Click "EMERGENCY STOP" for immediate halt

### Safety Requirements

⚠️ **IMPORTANT**: The script will only run if:
- The block directly underneath the player is `minecraft:iron_bars`
- Block state properties (connection states) are automatically ignored
- This condition is checked before starting and continuously during operation
- If the safety block changes, the script will emergency stop

### Block State Handling

The script now properly handles Minecraft block states. Iron bars can appear as:
- `minecraft:iron_bars` (simple form)
- `minecraft:iron_bars[east=false,north=false,south=false,waterlogged=false,west=true]` (with connection states)

The safety system extracts the base block name (`minecraft:iron_bars`) and ignores the connection properties in square brackets. This ensures reliable detection regardless of how the iron bars are connected to adjacent blocks.

## Configuration

The script includes several configurable parameters in the `MiningAutomation` class:

```python
# Boundaries
self.min_x = -26          # Minimum X coordinate
self.max_x = 29           # Maximum X coordinate

# Safety settings
self.safety_block = "minecraft:iron_bars"  # Required block underneath
self.safety_check_interval = 0.5           # Check every 500ms

# Performance settings
self.movement_delay = 0.05        # Delay between movement checks
self.transition_delay = 0.1       # Delay during direction changes

# Error handling
self.max_position_errors = 10     # Max position read failures
self.stuck_threshold = 5.0        # Seconds before considering stuck
```

## GUI Controls

### Window Features
- **Resizable Interface**: Drag corners to adjust window size to your preference
- **Scrollable Content**: Use mouse wheel to scroll through all sections
- **Responsive Layout**: All elements scale appropriately with window size
- **Minimum Size**: 400x400 pixels enforced for usability
- **Default Size**: 500x600 pixels, expandable to any larger size

### Status Display
- **Status**: Current automation state with real-time updates
- **Position**: Live player coordinates (X, Y, Z)
- **Statistics**: Runtime duration and direction change count

### Control Buttons
- **Start Mining**: Begins the automation after safety checks (scales with window width)
- **Stop Mining**: Gracefully stops automation and cleans up
- **EMERGENCY STOP**: Immediately halts all operations (red button with hover effects)

### Information Sections
- **Settings**: Shows current boundary settings and safety block requirements
- **Information**: Provides basic usage instructions
- **Usage Instructions**: Detailed step-by-step guide
- **GUI Tips**: Instructions for using the resizable interface

### Scrolling and Navigation
- **Mouse Wheel**: Scroll vertically through content
- **Keyboard**: Arrow keys and Page Up/Down also work for scrolling
- **Content Organization**: All sections clearly labeled and organized
- **Responsive Spacing**: Proper padding maintained at all window sizes

## Error Handling

The script handles various error conditions:

### Position Errors
- Retries up to 10 times for position reading failures
- Increases delay between attempts on errors
- Emergency stops if too many consecutive failures

### Stuck Detection
- Monitors player movement every cycle
- Triggers unstuck procedure if no movement for 5 seconds
- Attempts jumping and brief reverse movement

### Safety Violations
- Immediately stops if safety block changes
- Shows clear warning messages
- Prevents restart until safety conditions are restored

## Troubleshooting

### Common Issues

**Script won't start**
- Check that you're standing on iron bars
- Verify Minescript 4.0 is properly installed
- Run `\test_mining` to diagnose issues

**Player gets stuck**
- The script includes automatic unstuck detection
- Manual intervention may be needed for complex terrain
- Consider adjusting movement boundaries

**GUI doesn't appear**
- Ensure CustomTkinter is installed: `pip install customtkinter`
- Check for error messages in the Minecraft chat
- Try restarting Minecraft and Minescript
- See `INSTALL_CUSTOMTKINTER.md` for installation help

**Safety violations**
- Ensure the entire movement path has iron bars underneath
- Check for gaps or different blocks in the path
- Run `\debug_block_detection` to analyze block detection accuracy
- The safety system uses improved coordinate conversion (math.floor)
- The safety system is intentionally strict for protection

**Block detection issues**
- The script now uses `math.floor()` for accurate coordinate conversion
- Block state properties are automatically handled (connection states ignored)
- Debug output shows both full block strings and extracted base names
- Run `\debug_block_detection` and `\test_block_states` for comprehensive analysis
- Check that you're standing directly on iron bars, not adjacent blocks
- Iron bars with any connection state (east, west, north, south) are now properly detected

### Performance Tuning

Adjust these values for different performance characteristics:

- **Faster movement**: Reduce `movement_delay` (minimum 0.01)
- **Smoother transitions**: Increase `transition_delay`
- **More responsive safety**: Reduce `safety_check_interval`
- **Less stuck detection**: Increase `stuck_threshold`

## Technical Details

### Architecture
- **Main Thread**: GUI and user interaction
- **Automation Thread**: Movement and attack logic
- **Thread-safe Communication**: Status updates between threads

### API Usage
The script uses these key Minescript 4.0 functions:
- `player_position()`: Get current player coordinates
- `getblock()`: Check block types for safety
- `player_press_attack()`: Control attack state
- `player_press_forward/backward()`: Control movement
- `echo()`: Send messages to chat

### Safety Implementation
- Continuous monitoring of block underneath player
- Immediate stop on safety violation
- Pre-start validation prevents unsafe execution
- Emergency stop accessible at all times

## License

This script is provided as-is for educational and automation purposes. Use responsibly and in accordance with your server's rules and Minecraft's Terms of Service.

## Support

For issues or questions:
1. Run the test script first: `\test_mining`
2. Check the troubleshooting section above
3. Verify your Minescript 4.0 installation
4. Review the error messages in Minecraft chat
