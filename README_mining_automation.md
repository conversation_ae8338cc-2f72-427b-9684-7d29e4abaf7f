# Minescript 4.0 Mining Automation

A comprehensive automation script for Minecraft using Minescript 4.0 that provides automated mining with movement along the X-axis while continuously attacking.

## Features

### Core Functionality
- **Continuous Attack**: Maintains attack state throughout the entire operation
- **Automated Movement**: Moves player back and forth along X-axis between coordinates -26 and 29
- **Boundary Detection**: Automatically reverses direction when reaching boundaries
- **Smooth Transitions**: Includes brief pauses during direction changes for smooth movement

### GUI Interface
- **Modern CustomTkinter GUI**: Professional dark-themed interface with modern styling
- **Start/Stop Controls**: Easy-to-use buttons with hover effects and visual feedback
- **Emergency Stop**: Prominent red emergency button for immediate safety halt
- **Real-time Status**: Live updates of automation status and player position
- **Statistics Display**: Shows runtime and direction change count
- **Enhanced Visual Design**: Rounded buttons, better typography, and improved spacing

### Safety System
- **Iron Bars Detection**: Continuously checks that the block underneath is `minecraft:iron_bars`
- **Pre-start Validation**: Prevents script execution if safety conditions aren't met
- **Runtime Monitoring**: Triggers emergency stop if safety block changes during operation
- **Configurable Check Interval**: Safety checks every 500ms by default

### Error Handling
- **Connection Monitoring**: Detects and handles player disconnection
- **Stuck Detection**: Identifies when player isn't moving and attempts to unstuck
- **Position Error Recovery**: Handles temporary position reading failures
- **Graceful Degradation**: Continues operation despite minor errors

## Installation

1. **Install CustomTkinter** (required for modern GUI):
   ```bash
   pip install customtkinter
   ```
   See `INSTALL_CUSTOMTKINTER.md` for detailed installation instructions.

2. **Ensure Minescript 4.0** is installed and working

3. **Copy the script files** to your `minecraft/minescript/` directory:
   - `mining_automation.py` - Main automation script with modern GUI
   - `test_mining.py` - Test script for verification
   - `debug_block_detection.py` - Debug utility for block detection issues
   - `README_mining_automation.md` - This documentation
   - `INSTALL_CUSTOMTKINTER.md` - CustomTkinter installation guide

## Usage

### Pre-flight Testing
Before running the main automation, test your setup:

```
\test_mining
```

This will verify:
- Basic Minescript functions are working
- Movement controls respond correctly
- Safety conditions are met (with improved coordinate detection)
- Player is within target boundaries

If you encounter block detection issues, run the debug utility:

```
\debug_block_detection
```

This provides detailed analysis of:
- Player position and coordinate conversion
- Block detection methods comparison
- Area scanning around the player
- Iron bars detection testing

### Running the Automation

1. **Position yourself** on iron bars within the X coordinate range (-26 to 29)
2. **Run the script**: `\mining_automation`
3. **Use the GUI** to control the automation:
   - Click "Start Mining" to begin
   - Click "Stop Mining" for graceful shutdown
   - Click "EMERGENCY STOP" for immediate halt

### Safety Requirements

⚠️ **IMPORTANT**: The script will only run if:
- The block directly underneath the player is `minecraft:iron_bars`
- This condition is checked before starting and continuously during operation
- If the safety block changes, the script will emergency stop

## Configuration

The script includes several configurable parameters in the `MiningAutomation` class:

```python
# Boundaries
self.min_x = -26          # Minimum X coordinate
self.max_x = 29           # Maximum X coordinate

# Safety settings
self.safety_block = "minecraft:iron_bars"  # Required block underneath
self.safety_check_interval = 0.5           # Check every 500ms

# Performance settings
self.movement_delay = 0.05        # Delay between movement checks
self.transition_delay = 0.1       # Delay during direction changes

# Error handling
self.max_position_errors = 10     # Max position read failures
self.stuck_threshold = 5.0        # Seconds before considering stuck
```

## GUI Controls

### Status Display
- **Status**: Current automation state
- **Position**: Real-time player coordinates
- **Statistics**: Runtime and direction change count

### Control Buttons
- **Start Mining**: Begins the automation after safety checks
- **Stop Mining**: Gracefully stops automation and cleans up
- **EMERGENCY STOP**: Immediately halts all operations (red button)

### Information Panel
- Shows current boundary settings
- Displays safety block requirement
- Provides usage instructions

## Error Handling

The script handles various error conditions:

### Position Errors
- Retries up to 10 times for position reading failures
- Increases delay between attempts on errors
- Emergency stops if too many consecutive failures

### Stuck Detection
- Monitors player movement every cycle
- Triggers unstuck procedure if no movement for 5 seconds
- Attempts jumping and brief reverse movement

### Safety Violations
- Immediately stops if safety block changes
- Shows clear warning messages
- Prevents restart until safety conditions are restored

## Troubleshooting

### Common Issues

**Script won't start**
- Check that you're standing on iron bars
- Verify Minescript 4.0 is properly installed
- Run `\test_mining` to diagnose issues

**Player gets stuck**
- The script includes automatic unstuck detection
- Manual intervention may be needed for complex terrain
- Consider adjusting movement boundaries

**GUI doesn't appear**
- Ensure CustomTkinter is installed: `pip install customtkinter`
- Check for error messages in the Minecraft chat
- Try restarting Minecraft and Minescript
- See `INSTALL_CUSTOMTKINTER.md` for installation help

**Safety violations**
- Ensure the entire movement path has iron bars underneath
- Check for gaps or different blocks in the path
- Run `\debug_block_detection` to analyze block detection accuracy
- The safety system uses improved coordinate conversion (math.floor)
- The safety system is intentionally strict for protection

**Block detection issues**
- The script now uses `math.floor()` for accurate coordinate conversion
- Debug output shows exact coordinates being checked
- Run `\debug_block_detection` for comprehensive analysis
- Check that you're standing directly on iron bars, not adjacent blocks

### Performance Tuning

Adjust these values for different performance characteristics:

- **Faster movement**: Reduce `movement_delay` (minimum 0.01)
- **Smoother transitions**: Increase `transition_delay`
- **More responsive safety**: Reduce `safety_check_interval`
- **Less stuck detection**: Increase `stuck_threshold`

## Technical Details

### Architecture
- **Main Thread**: GUI and user interaction
- **Automation Thread**: Movement and attack logic
- **Thread-safe Communication**: Status updates between threads

### API Usage
The script uses these key Minescript 4.0 functions:
- `player_position()`: Get current player coordinates
- `getblock()`: Check block types for safety
- `player_press_attack()`: Control attack state
- `player_press_forward/backward()`: Control movement
- `echo()`: Send messages to chat

### Safety Implementation
- Continuous monitoring of block underneath player
- Immediate stop on safety violation
- Pre-start validation prevents unsafe execution
- Emergency stop accessible at all times

## License

This script is provided as-is for educational and automation purposes. Use responsibly and in accordance with your server's rules and Minecraft's Terms of Service.

## Support

For issues or questions:
1. Run the test script first: `\test_mining`
2. Check the troubleshooting section above
3. Verify your Minescript 4.0 installation
4. Review the error messages in Minecraft chat
