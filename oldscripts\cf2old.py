import minescript
import time
import json
import http.client

WEBHOOK_URL = "/api/webhooks/1283152937010335921/iVy802hligbyKb-yCfXhNhRI7K2b2jlQtlwIlQkVfuHegX4tsC3JXM_ILS433mBi371b"
DISCORD_SERVER = "discord.com"

def send_discord_message(content):
    conn = http.client.HTTPSConnection(DISCORD_SERVER)
    
    headers = {
        'Content-Type': 'application/json'
    }
    
    body = json.dumps({
        'content': content
    })
    
    conn.request("POST", WEBHOOK_URL, body, headers)
    response = conn.getresponse()
    if response.status != 204:
        minescript.echo(f"Failed to send message: {response.status} {response.reason}")
    conn.close()

def on_chat_message(message):
    # Check if the message includes the specific format
    if "made a ? Coinflip ? for:" in message and "GC" in message:
        # Send the message content to Discord via webhook
        send_discord_message(f"{message}")

def main():
    # Unregister any existing chat message listeners
    try:
        minescript.unregister_chat_message_listener()
    except RuntimeError:
        pass  # Ignore if no listener is registered
    
    # Register the chat message listener
    minescript.register_chat_message_listener(on_chat_message)

    # Echo that the chat listener has started
    minescript.echo("Chat listener started. Listening for specific messages...")
    
    try:
        while True:
            # Continuously check for new chat messages
            time.sleep(1)
    except KeyboardInterrupt:
        # Unregister the chat message listener on script termination
        minescript.unregister_chat_message_listener()
        minescript.echo("Chat listener stopped.")

if __name__ == "__main__":
    main()