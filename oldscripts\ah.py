import time
import mss
import requests
import os
import pyautogui
from PIL import Image
import minescript

# Function to send the screenshot to a Discord webhook
def send_screenshot_to_discord(screenshot_path, webhook_url):
    with open(screenshot_path, 'rb') as f:
        file = {
            "file": f
        }
        response = requests.post(webhook_url, files=file)
        if response.status_code == 200:
            print("Screenshot sent to Discord successfully.")
        else:
            print(f"Failed to send screenshot to Discord. Status code: {response.status_code}")

# Function to extract text and check for "Miscellaneous"
def extract_text_value(item_str):
    # Check if the text contains "Miscellaneous"
    if "Miscellaneous" in item_str:
        return item_str  # Return the item string if "Miscellaneous" is found
    else:
        return None  # Return None if "Miscellaneous" is not found

# Main function to monitor slot 0
def main():
    # Initialize a variable to track the previous item in slot 0
    previous_item = None
    webhook_url = 'https://discord.com/api/webhooks/1294588808645574677/m6p3DWfAnV9yUyDFevpRGXRUW8ouGiicQmzEC6odLFuRQusAS8P_vUsBzB1Rt1HnM1t-'  # Your Discord webhook

    # Execute the "/ah" command at the start
    minescript.execute("/ah")
    time.sleep(3)  # Give it a few seconds to load the auction house

    # Continuously check for updates in slot 0
    while True:
        # Retrieve all items in the container
        container_items = minescript.container_get_items()

        # If the auction house is not open, reopen it
        if container_items is None or not any(item.slot == 0 for item in container_items):
            print("Reopening Auction House...")
            minescript.execute("/ah")
            time.sleep(3)  # Wait for the auction house to open again
            continue

        # Check if there is an item in slot 0
        for item in container_items:
            if item.slot == 0:  # Check if the item is in slot 0
                # Convert the item to a string and extract the 'text' values
                item_str = str(item)
                text_values = extract_text_value(item_str)
                
                # Proceed only if "Miscellaneous" is found
                if text_values is None:
                    continue  # If "Miscellaneous" is not found, skip this item

                # Check if the current item is different from the previous one
                if previous_item == text_values:
                    # If the current item is the same as the previous, skip it
                    print("Same item, skipping...")
                    break

                # Update the previous item to the current one
                previous_item = text_values

                # Move the mouse cursor to slot 0 coordinates (808, 359)
                slot_0_x, slot_0_y = 808, 359
                pyautogui.moveTo(slot_0_x, slot_0_y)
                print(f"Moved mouse to {slot_0_x}, {slot_0_y}")

                # Wait a moment to simulate hovering
                time.sleep(1)

                # Take a screenshot of the updated slot using mss
                screenshot_path = r"C:\Users\<USER>\AppData\Roaming\.minecraft\minescript\auctionhouse\auction_house_slot_0.png"
                with mss.mss() as sct:
                    # Region coordinates as a tuple: (left, top, width, height)
                    region = {"left": 796, "top": 314, "width": 605, "height": 593}
                    screenshot = sct.grab(region)

                    # Convert to Image using PIL and save it
                    img = Image.frombytes("RGB", screenshot.size, screenshot.rgb)
                    img.save(screenshot_path)
                
                # Send the screenshot to the Discord webhook
                send_screenshot_to_discord(screenshot_path, webhook_url)

                break  # Stop checking once we've processed slot 0

        # Wait before checking again (adjust the sleep duration as needed)
        time.sleep(5)  # Check every 5 seconds for new items

if __name__ == "__main__":
    main()