import minescript
import time
import re
import http.client
import json
import os

# Define the log file path
LOG_FILE_PATH = r'C:\Users\<USER>\AppData\Roaming\.minecraft\minescript\coinflip_results.txt'

# Webhook URLs
COINFLIP_CREATION_STREAK_WEBHOOK_URL = "https://discord.com/api/webhooks/1283152937010335921/iVy802hligbyKb-yCfXhNhRI7K2b2jlQtlwIlQkVfuHegX4tsC3JXM_ILS433mBi371b"
WIN_LOSS_STREAK_WEBHOOK_URL = "https://discord.com/api/webhooks/1282979107587493908/vCr0Ae0jYEPH4iD1BJRz1o_OrqdIClv81LoxJNK9LAEZ3Qw4ArAUHudMR3K3ycn8qapg"

# Dictionaries to keep track of total wins/losses and current streaks
player_stats = {}
player_streaks = {}
total_wins = 0
total_losses = 0

# Delay settings
DISCORD_DELAY = 0.3  # 300 milliseconds delay for Discord messages
ECHO_DELAY = 0.3  # 300 milliseconds delay for echo messages

def write_to_log(message):
    with open(LOG_FILE_PATH, 'a', encoding='utf-8') as file:
        file.write(message + '\n')

def load_stats_from_log():
    global total_wins, total_losses
    
    if not os.path.exists(LOG_FILE_PATH):
        return
    
    with open(LOG_FILE_PATH, 'r', encoding='utf-8') as file:
        lines = file.readlines()

    for line in lines:
        if "Won the coinflip against" in line:
            player_name = line.split("Won the coinflip against")[1].split("for")[0].strip().split(" ")[-1]
            update_stats(player_name, 'loss')  # Opponent lost, player won
            update_stats('you', 'win')  # You won
            total_wins += 1
        elif "Lost the coinflip against" in line:
            player_name = line.split("Lost the coinflip against")[1].split("for")[0].strip().split(" ")[-1]
            update_stats(player_name, 'win')  # Opponent won, you lost
            update_stats('you', 'loss')  # You lost
            total_losses += 1

def update_stats(player_name, result):
    if player_name not in player_stats:
        player_stats[player_name] = {'win': 0, 'loss': 0}
    
    if player_name not in player_streaks:
        player_streaks[player_name] = {'current_streak': 0, 'streak_type': None}

    if result == 'win':
        player_stats[player_name]['win'] += 1
        if player_streaks[player_name]['streak_type'] == 'win':
            player_streaks[player_name]['current_streak'] += 1
        else:
            player_streaks[player_name]['current_streak'] = 1
            player_streaks[player_name]['streak_type'] = 'win'
    elif result == 'loss':
        player_stats[player_name]['loss'] += 1
        if player_streaks[player_name]['streak_type'] == 'loss':
            player_streaks[player_name]['current_streak'] += 1
        else:
            player_streaks[player_name]['current_streak'] = 1
            player_streaks[player_name]['streak_type'] = 'loss'

def calculate_win_rate(wins, losses):
    total = wins + losses
    if total == 0:
        return 0.0
    return (wins / total) * 100

def calculate_overall_win_rate():
    return calculate_win_rate(total_wins, total_losses)

def calculate_net_gc(player_name):
    if not os.path.exists(LOG_FILE_PATH):
        return 0
    
    net_gc = 0
    
    with open(LOG_FILE_PATH, 'r', encoding='utf-8') as file:
        lines = file.readlines()
    
    for line in lines:
        if f"Won the coinflip against {player_name}" in line:
            amount = extract_gc_amount(line)
            net_gc += amount  # Add to net GC if won
        elif f"Lost the coinflip against {player_name}" in line:
            amount = extract_gc_amount(line)
            net_gc -= amount  # Subtract from net GC if lost
    
    return net_gc

def extract_gc_amount(text):
    match = re.search(r"(\d+) GC", text)
    if match:
        return int(match.group(1))  # Return the matched number as an integer
    return 0  # Return 0 if no match is found

def get_streak_message(player_name, for_discord=False):
    if player_name in player_streaks:
        current_streak = player_streaks[player_name]['current_streak']
        streak_type = player_streaks[player_name]['streak_type']

        # Win/Loss stats for the player
        losses = player_stats[player_name].get('win', 0)
        wins = player_stats[player_name].get('loss', 0)
        win_rate = calculate_win_rate(wins, losses)

        # Calculate net GC
        net_gc = calculate_net_gc(player_name)
        if net_gc > 0:
            gc_message = f"UP {net_gc} GC"
        elif net_gc < 0:
            gc_message = f"DOWN {-net_gc} GC"
        else:
            gc_message = "EVEN"

        overall_win_rate = calculate_overall_win_rate()

        # Format for Discord
        if for_discord:
            if streak_type == 'win':
                return f"🔴 **{player_name}** is on a **{current_streak}-win** streak against you! WR: **{win_rate:.2f}%** | **{wins}W/{losses}L** | **{gc_message}** | **OVERALL WR: {overall_win_rate:.2f}%** | **{total_wins}W/{total_losses}L**"
            else:
                return f"🟢 **{player_name}** is on a **{current_streak}-loss** streak against you! WR: **{win_rate:.2f}%** | **{wins}W/{losses}L** | **{gc_message}** | **OVERALL WR: {overall_win_rate:.2f}%** | **{total_wins}W/{total_losses}L**"

        # Format for Minecraft
        else:
            win_loss_message = f"§a{wins}W§r/§c{losses}L§r"
            if net_gc > 0:
                gc_message = f"§aUP {net_gc} GC"
            elif net_gc < 0:
                gc_message = f"§cDOWN {-net_gc} GC"
            else:
                gc_message = "EVEN"

            if streak_type == 'win':
                return f"§c{player_name} is on a {current_streak}-win streak against you! §9WR: {win_rate:.2f}%§r | {win_loss_message} | {gc_message} | §dOVERALL WR: {overall_win_rate:.2f}% | {total_wins}W/{total_losses}L§r"
            else:
                return f"§a{player_name} is on a {current_streak}-loss streak against you! §9WR: {win_rate:.2f}%§r | {win_loss_message} | {gc_message} | §dOVERALL WR: {overall_win_rate:.2f}% | {total_wins}W/{total_losses}L§r"
    
    else:
        if for_discord:
            return f"🟡 **{player_name}** has no current streak against you."
        else:
            return f"{player_name} has no current streak against you."

def send_discord_message(content, webhook_url):
    try:
        conn = http.client.HTTPSConnection("discord.com")
        
        headers = {
            'Content-Type': 'application/json'
        }
        
        body = json.dumps({
            'content': content
        })
        
        conn.request("POST", webhook_url, body, headers)
        response = conn.getresponse()
        response_body = response.read().decode()
        
        if response.status != 204:
            minescript.echo(f"Failed to send message: {response.status} {response.reason}")
            minescript.echo(f"Response body: {response_body}")
    except Exception as e:
        minescript.echo(f"Exception occurred: {str(e)}")
    finally:
        conn.close()

def on_chat_message(message):
    global player_stats, player_streaks, total_wins, total_losses

    try:
        # Check if the message includes the specific format for coinflip creation
        if "made a ? Coinflip ? for:" in message and "GC" in message:
            # Updated regex to allow player names to start with a period
            match = re.search(r"\[.*?\] ([\.\w][\w\*]*) made a .* Coinflip .* for: (\d+ GC)", message)
            if match:
                player_name = match.group(1)  # Extract the player name
                amount = match.group(2)  # Extract the amount

                # Update streak and stats information
                streak_message = get_streak_message(player_name, for_discord=True)
                
                # Prepare Discord messages
                discord_coinflip_message = f"🪙 **{player_name}** made a CF for **{amount}** <@&1283550625875103876>"
                discord_streak_message = streak_message
                
                # Delay for both echo and Discord messages
                time.sleep(ECHO_DELAY)
                minescript.echo(get_streak_message(player_name))
                
                # Send coinflip creation message to Discord
                time.sleep(DISCORD_DELAY)
                send_discord_message(discord_coinflip_message, COINFLIP_CREATION_STREAK_WEBHOOK_URL)
                
                # Send streak message to Discord
                time.sleep(DISCORD_DELAY)
                send_discord_message(discord_streak_message, COINFLIP_CREATION_STREAK_WEBHOOK_URL)
                
            else:
                minescript.echo("Regex did not match the coinflip creation message.")

        # Check for win/loss messages
        elif "You have won the coinflip against" in message:
            parts = message.split("You have won the coinflip against")
            player_info = parts[1].split("for")
            player_details = player_info[0].strip().split(" ")[-1]  # Get the player name without rank
            amount_details = player_info[1].strip()
            formatted_discord = f"📈 Won the coinflip against **{player_details}** for **{amount_details}**"
            formatted_message = f"Won the coinflip against {player_details} for {amount_details}"
            write_to_log(formatted_message)
            send_discord_message(formatted_discord, WIN_LOSS_STREAK_WEBHOOK_URL)
            
            # Update stats: you won, opponent lost
            update_stats(player_details, 'loss')  # Opponent lost
            update_stats('you', 'win')  # You won
            total_wins += 1
            
            # Echo win streak
            time.sleep(ECHO_DELAY)
            streak_message = get_streak_message(player_details)
            minescript.echo(streak_message)
            
            # Send win streak to Discord
            time.sleep(DISCORD_DELAY)
            discord_message = get_streak_message(player_details, for_discord=True)
            send_discord_message(discord_message, WIN_LOSS_STREAK_WEBHOOK_URL)

        elif "You have lost the coinflip against" in message:
            parts = message.split("You have lost the coinflip against")
            player_info = parts[1].split("for")
            player_details = player_info[0].strip().split(" ")[-1]  # Get the player name without rank
            amount_details = player_info[1].strip()
            formatted_discord = f"📉 Lost the coinflip against **{player_details}** for **{amount_details}**"
            formatted_message = f"Lost the coinflip against {player_details} for {amount_details}"
            write_to_log(formatted_message)
            send_discord_message(formatted_discord, WIN_LOSS_STREAK_WEBHOOK_URL)
            
            # Update stats: you lost, opponent won
            update_stats(player_details, 'win')  # Opponent won
            update_stats('you', 'loss')  # You lost
            total_losses += 1
            
            # Echo loss streak
            time.sleep(ECHO_DELAY)
            streak_message = get_streak_message(player_details)
            minescript.echo(streak_message)
            
            # Send loss streak to Discord
            time.sleep(DISCORD_DELAY)
            discord_message = get_streak_message(player_details, for_discord=True)
            send_discord_message(discord_message, WIN_LOSS_STREAK_WEBHOOK_URL)
    
    except Exception as e:
        minescript.echo(f"Exception occurred in on_chat_message: {str(e)}")


def main():
    # Load past stats and streaks from log file
    load_stats_from_log()

    # Unregister any existing chat message listeners
    try:
        minescript.unregister_chat_message_listener()
    except RuntimeError:
        pass  # Ignore if no listener is registered
    
    # Register the chat message listener
    minescript.register_chat_message_listener(on_chat_message)
    minescript.echo("Coinflip chat listener started.")
    
    # Main loop to keep the listener running
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        # Unregister the listener on script exit
        minescript.unregister_chat_message_listener()
        minescript.echo("Coinflip chat listener stopped.")

if __name__ == "__main__":
    main()
