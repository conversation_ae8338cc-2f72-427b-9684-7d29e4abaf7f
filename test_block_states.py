#!/usr/bin/env python3
"""
Block State Testing Script
=========================

This script tests the block state handling functionality to ensure the mining
automation correctly identifies iron_bars regardless of their connection states.

Usage: \test_block_states
"""

import minescript


def extract_base_block_name(block_string):
    """
    Extract the base block name from a full Minecraft block string.
    Same function as in mining_automation.py for consistency.
    """
    if not block_string:
        return block_string
    
    bracket_index = block_string.find('[')
    
    if bracket_index == -1:
        return block_string
    else:
        return block_string[:bracket_index]


def test_block_state_extraction():
    """Test the block state extraction function with various examples"""
    minescript.echo("=== Testing Block State Extraction ===")
    
    test_cases = [
        # (input, expected_output, description)
        ("minecraft:iron_bars", "minecraft:iron_bars", "Simple block name"),
        ("minecraft:iron_bars[east=false,north=false,south=false,waterlogged=false,west=true]", 
         "minecraft:iron_bars", "Iron bars with connection states"),
        ("minecraft:stone", "minecraft:stone", "Simple stone block"),
        ("minecraft:oak_door[facing=north,half=lower,hinge=left,open=false,powered=false]",
         "minecraft:oak_door", "Door with multiple properties"),
        ("air", "air", "Air block"),
        ("", "", "Empty string"),
        ("minecraft:redstone_wire[east=side,north=none,power=0,south=up,west=none]",
         "minecraft:redstone_wire", "Redstone wire with complex states"),
    ]
    
    passed = 0
    total = len(test_cases)
    
    for input_str, expected, description in test_cases:
        result = extract_base_block_name(input_str)
        success = result == expected
        
        status = "✓" if success else "✗"
        minescript.echo(f"{status} {description}")
        minescript.echo(f"  Input:    '{input_str}'")
        minescript.echo(f"  Expected: '{expected}'")
        minescript.echo(f"  Got:      '{result}'")
        
        if success:
            passed += 1
        else:
            minescript.echo(f"  ERROR: Extraction failed!")
        
        minescript.echo("")  # Empty line for readability
    
    minescript.echo(f"Block state extraction test: {passed}/{total} passed")
    return passed == total


def test_deepslate_bricks_variations():
    """Test deepslate bricks detection with various possible block states"""
    minescript.echo("=== Testing Deepslate Bricks Block State Variations ===")

    # Common deepslate bricks block state combinations (deepslate bricks typically don't have states, but testing various forms)
    deepslate_bricks_examples = [
        "minecraft:deepslate_bricks",
        "minecraft:deepslate_bricks[]",  # Empty state properties
        "deepslate_bricks",  # Without namespace
        "minecraft:deepslate_brick",  # Singular form (if it exists)
        "deepslate_brick",  # Singular without namespace
    ]

    expected_base = "minecraft:deepslate_bricks"
    passed = 0
    total = len(deepslate_bricks_examples)

    for example in deepslate_bricks_examples:
        result = extract_base_block_name(example)
        success = result == expected_base or result == example  # Allow exact match for variants

        status = "✓" if success else "✗"
        minescript.echo(f"{status} {example}")
        minescript.echo(f"  Extracted: '{result}'")

        if success:
            passed += 1
        else:
            minescript.echo(f"  INFO: Expected '{expected_base}' or exact match, got '{result}'")

        minescript.echo("")

    minescript.echo(f"Deepslate bricks variation test: {passed}/{total} passed")
    return passed == total


def test_current_block_detection():
    """Test block detection at current player position"""
    minescript.echo("=== Testing Current Block Detection ===")
    
    try:
        import math
        pos = minescript.player_position()
        
        player_x, player_y, player_z = pos
        block_x = math.floor(player_x)
        block_y = math.floor(player_y) - 1  # Block underneath
        block_z = math.floor(player_z)
        
        minescript.echo(f"Player position: X={player_x:.3f}, Y={player_y:.3f}, Z={player_z:.3f}")
        minescript.echo(f"Checking block at: X={block_x}, Y={block_y}, Z={block_z}")
        
        # Get the actual block
        block_full = minescript.getblock(block_x, block_y, block_z)
        block_base = extract_base_block_name(block_full)
        
        minescript.echo(f"Full block string: '{block_full}'")
        minescript.echo(f"Base block name: '{block_base}'")
        
        # Test the safety check logic
        expected_safety_block = "minecraft:deepslate_bricks"
        is_safe = block_base == expected_safety_block

        minescript.echo(f"Expected safety block: '{expected_safety_block}'")
        minescript.echo(f"Safety check result: {is_safe}")

        if is_safe:
            minescript.echo("✓ Current location is SAFE for mining automation")

            # Show block state properties if present
            if '[' in block_full:
                state_part = block_full[block_full.find('['):]
                minescript.echo(f"Block state properties: {state_part}")
                minescript.echo("Note: Block states are now properly ignored by the safety system")
            else:
                minescript.echo("No block state properties detected")
        else:
            minescript.echo("⚠ Current location is NOT SAFE for mining automation")
            minescript.echo(f"Move to a location with {expected_safety_block} underneath")
        
        return is_safe
        
    except Exception as e:
        minescript.echo(f"Error in current block detection: {e}")
        return False


def test_safety_system_compatibility():
    """Test compatibility with the mining automation safety system"""
    minescript.echo("=== Testing Safety System Compatibility ===")
    
    # Simulate the exact logic used in mining_automation.py
    safety_block = "minecraft:deepslate_bricks"
    
    try:
        import math
        pos = minescript.player_position()
        
        player_x, player_y, player_z = pos
        block_x = math.floor(player_x)
        block_y = math.floor(player_y) - 1
        block_z = math.floor(player_z)
        
        # Get the block (same as mining automation)
        block_below_full = minescript.getblock(block_x, block_y, block_z)
        block_below_base = extract_base_block_name(block_below_full)
        expected_base = extract_base_block_name(safety_block)
        
        minescript.echo("Simulating mining automation safety check:")
        minescript.echo(f"  Full block detected: '{block_below_full}'")
        minescript.echo(f"  Base block name: '{block_below_base}'")
        minescript.echo(f"  Expected base block: '{expected_base}'")
        
        # Perform the comparison (same as mining automation)
        safety_result = block_below_base == expected_base
        
        minescript.echo(f"  Safety check result: {safety_result}")
        
        if safety_result:
            minescript.echo("✓ Mining automation safety check would PASS")
        else:
            minescript.echo("⚠ Mining automation safety check would FAIL")
        
        return safety_result
        
    except Exception as e:
        minescript.echo(f"Error in safety system test: {e}")
        return False


def run_all_tests():
    """Run all block state tests"""
    minescript.echo("Starting Block State Testing...")
    minescript.echo("=" * 60)
    
    tests = [
        ("Block State Extraction", test_block_state_extraction),
        ("Deepslate Bricks Variations", test_deepslate_bricks_variations),
        ("Current Block Detection", test_current_block_detection),
        ("Safety System Compatibility", test_safety_system_compatibility)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        minescript.echo(f"\nRunning {test_name}...")
        if test_func():
            passed += 1
        minescript.echo("-" * 40)
    
    minescript.echo("\n" + "=" * 60)
    minescript.echo(f"Block State Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        minescript.echo("✓ All tests passed! Block state handling is working correctly.")
        minescript.echo("  The mining automation should now properly detect deepslate_bricks")
        minescript.echo("  regardless of any potential block state properties.")
    else:
        minescript.echo("⚠ Some tests failed. Block state handling may have issues.")
    
    return passed == total


def main():
    """Main entry point"""
    try:
        run_all_tests()
    except Exception as e:
        minescript.echo(f"Block state test error: {e}")
        import traceback
        minescript.echo(f"Traceback: {traceback.format_exc()}")


if __name__ == "__main__":
    main()
