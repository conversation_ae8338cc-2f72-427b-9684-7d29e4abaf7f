import time
import pyautogui
import minescript

def rotate_player_on_axes(x_rotation=None, y_rotation=None):
    # Get the player's current orientation
    current_yaw, current_pitch = minescript.player_orientation()

    # Apply the Y-axis rotation if specified
    if y_rotation == minescript.Rotations.Y_90:
        new_yaw = (current_yaw + 90) % 360  # Rotate 90 degrees on the Y-axis
    else:
        new_yaw = current_yaw

    # Apply the X-axis rotation if specified
    if x_rotation == minescript.Rotations.X_90:
        new_pitch = (current_pitch + 90) % 360  # Rotate 90 degrees on the X-axis
    else:
        new_pitch = current_pitch

    # Set the player's new orientation
    minescript.player_set_orientation(new_yaw, new_pitch)

def run_script():
    # Execute /is command
    minescript.execute("/is")
    
    # Wait 200ms (doubled from 100ms)
    time.sleep(0.2)
    
    # Move mouse to (1065, 466), wait 50ms, then click
    pyautogui.moveTo(1065, 466)
    time.sleep(0.05)
    pyautogui.click()
    
    # Wait 600ms (doubled from 300ms)
    time.sleep(0.6)
    
    # Move mouse to (924, 447), wait 50ms, then click
    pyautogui.moveTo(924, 447)
    time.sleep(0.05)
    pyautogui.click()
    
    # Wait 600ms (doubled from 300ms)
    time.sleep(0.6)
    
    # Move mouse to (996, 446), wait 50ms, then click
    pyautogui.moveTo(996, 446)
    time.sleep(0.05)
    pyautogui.click()
    
    # Wait 1000ms (doubled from 500ms)
    time.sleep(1.0)
    
    # Rotate the player on Y-axis by 90 degrees using predefined constant
    rotate_player_on_axes(y_rotation=minescript.Rotations.Y_90)
    
    # Wait 200ms (doubled from 100ms)
    time.sleep(0.2)
    
    # Hold left key and rotate 90 degrees simultaneously
    minescript.player_press_left(True)
    
    # Rotate player on X-axis by 90 degrees while holding the left key using predefined constant
    rotate_player_on_axes(x_rotation=minescript.Rotations.X_90)
    
    # Wait 400ms (changed from 200ms)
    time.sleep(0.4)
    minescript.player_press_left(False)
    
    # Hold W for 4 seconds
    minescript.player_press_forward(True)
    time.sleep(4.0)
    minescript.player_press_forward(False)

if __name__ == "__main__":
    run_script()
