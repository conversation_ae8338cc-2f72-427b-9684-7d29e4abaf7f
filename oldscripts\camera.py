import time
import mss
import requests
import os
from PIL import Image
import minescript

# Function to send the screenshot to a Discord webhook
def send_screenshot_to_discord(screenshot_path, webhook_url):
    if not os.path.isfile(screenshot_path):
        print(f"Error: File '{screenshot_path}' does not exist.")
        return

    try:
        with open(screenshot_path, 'rb') as f:
            file = {"file": f}
            response = requests.post(webhook_url, files=file)
            if response.status_code == 200:
                print("Screenshot sent to Discord successfully.")
            else:
                print(f"Failed to send screenshot to Discord. Status code: {response.status_code}")
                print(f"Response content: {response.content.decode()}")
    except Exception as e:
        print(f"Error sending screenshot: {e}")

def take_screenshot(screenshot_path):
    try:
        # Wait for 10 seconds before taking the screenshot
        print("Waiting for 10 seconds before taking the screenshot...")
        time.sleep(10)

        with mss.mss() as sct:
            # Capture the entire screen
            screenshot = sct.grab(sct.monitors[1])  # sct.monitors[1] is usually the main monitor

            # Convert to Image using PIL and save it
            img = Image.frombytes("RGB", screenshot.size, screenshot.rgb)
            img.save(screenshot_path)
            print(f"Screenshot saved to {screenshot_path}.")
    except Exception as e:
        print(f"Error taking screenshot: {e}")

def run_script():
    # Execute /survival command
    minescript.execute("/survival")
    minescript.flush()  # Ensure execution is complete
    print("Executed /survival command.")
    
    # Define the screenshot path
    screenshot_path = r"C:\Users\<USER>\AppData\Roaming\.minecraft\minescript\hourly_screenshot.png"
    
    # Take a full-screen screenshot
    take_screenshot(screenshot_path)
    
    # Send screenshot to Discord
    discord_webhook_url = "https://discord.com/api/webhooks/1300042099952975912/T0nFg0Ek5Y7J3U-_v9LEQhTKT7p6FfG3ccxxQ9NvVzKfkkYXAusRmMP98Ob-N1znp1Gb"
    send_screenshot_to_discord(screenshot_path, discord_webhook_url)

    # Execute /skyblock command
    minescript.execute("/skyblock")
    minescript.flush()  # Ensure execution is complete
    print("Executed /skyblock command.")

def schedule_script():
    # Schedule this script to run every hour
    while True:
        run_script()
        print("Waiting for one hour...")
        time.sleep(3540)
        minescript.execute("/msg Cerv Yo flick up rq")
        time.sleep(60)
if __name__ == "__main__":
    schedule_script()
