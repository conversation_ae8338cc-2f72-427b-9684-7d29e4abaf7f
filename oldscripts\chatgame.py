from minescript import EventQueue, echo
import openai  # Make sure to have this module installed and API key set

openai.api_key = "********************************************************************************************************************************************************************"

def unscramble_word(word):
    prompt = f"Unscramble the word: {word}"
    response = openai.Completion.create(
        engine="text-davinci-003",
        prompt=prompt,
        max_tokens=10
    )
    return response.choices[0].text.strip()

def main():
    with EventQueue() as event_queue:
        event_queue.register_chat_listener()
        while True:
            event = event_queue.get()
            if event.type == "CHAT" and "! Unscramble the word (" in event.message:
                # Extract the word inside the parentheses
                start_idx = event.message.find("(") + 1
                end_idx = event.message.find(")")
                if start_idx != -1 and end_idx != -1:
                    word = event.message[start_idx:end_idx]
                    if word:
                        unscrambled = unscramble_word(word)
                        echo(f"Unscrambled word: {unscrambled}")

if __name__ == "__main__":
    main()
