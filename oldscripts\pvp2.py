import time
import re
import threading
import tkinter as tk
from tkinter import ttk
from tkinter import messagebox
from draw_text import draw_centered_string
from minescript import EventQueue, EventType
import os
import queue  # Import the queue module

# Path to the config file
CONFIG_FILE_PATH = r'C:\Users\<USER>\AppData\Roaming\.minecraft\minescript\pvpconfig.txt'

# Global variables to store chat messages and their text objects
chat_messages = []
chat_text_objects = []
message_x = 238  # Default X position for the messages
new_message_y = 80  # Default Y position for the newest message
show_rune_messages = True  # Toggle to show or hide rune messages
show_runic_obstruction = True  # Toggle to show or hide Runic Obstruction messages
test_message_displayed = False  # Flag to track the visibility of the test message
test_message_object = None  # Store the text object for the test message
last_update_time = time.time()  # Track time for message updates

# Regular expressions to extract information from the messages
pattern_1 = re.compile(r"RUNES.*You have disabled the rune effects of (.*) with your Runic Obstruction .* for (\d+) seconds.")
pattern_2 = re.compile(r"RUNES.*(.*) has prevented your custom enchants from working for (\d+) seconds with their Runic Obstruction .*.")
pattern_3 = re.compile(r"RUNES.* (.*) has a combo of 3 on you! Your Ra's Wrath IV has activated!")  # Updated pattern for Ra's Wrath

# Store the last time the countdown was updated
last_update_time = time.time()

# Config file management functions
def read_config():
    """Read configuration from pvpconfig.txt."""
    global message_x, new_message_y, show_rune_messages, show_runic_obstruction
    if os.path.exists(CONFIG_FILE_PATH):
        with open(CONFIG_FILE_PATH, 'r') as file:
            for line in file:
                key, value = line.strip().split('=')
                if key == 'message_x':
                    message_x = int(value)
                elif key == 'message_y':
                    new_message_y = int(value)
                elif key == 'show_rune_messages':
                    show_rune_messages = value.lower() == 'true'
                elif key == 'show_runic_obstruction':
                    show_runic_obstruction = value.lower() == 'true'
    else:
        save_config()  # If config doesn't exist, create it with default values

def save_config():
    """Save current configuration to pvpconfig.txt."""
    with open(CONFIG_FILE_PATH, 'w', encoding='utf-8') as file:  # Specify encoding
        file.write(f"message_x={message_x}\n")
        file.write(f"message_y={new_message_y}\n")
        file.write(f"show_rune_messages={show_rune_messages}\n")
        file.write(f"show_runic_obstruction={show_runic_obstruction}\n")

# Update and display the test message
def toggle_test_message():
    global test_message_displayed, test_message_object

    if test_message_displayed:
        # Remove the test message if it's displayed
        if test_message_object and test_message_object in chat_text_objects:
            chat_text_objects.remove(test_message_object)
        if test_message_object:  # Only clear the text if the object exists
            test_message_object.string.set_value("")  # Clear the text
        test_message_displayed = False
    else:
        # Show the test message if it's not displayed
        test_message_object = display_message("Test message", 0x00ff00, 5)  # Green color with 5 seconds
        test_message_displayed = True

# Functions to update X and Y position and save them to the config
def update_message_x(value):
    global message_x
    message_x = int(float(value))  # Convert the float to an int
    save_config()  # Save the updated X position to config

def update_new_message_y(value):
    global new_message_y
    new_message_y = int(float(value))  # Convert the float to an int
    save_config()  # Save the updated Y position to config

# Function to toggle the display of rune messages
def toggle_rune_messages():
    global show_rune_messages
    show_rune_messages = not show_rune_messages
    save_config()  # Save the toggle state to config

# Function to toggle the display of Runic Obstruction messages
def toggle_runic_obstruction():
    global show_runic_obstruction
    show_runic_obstruction = not show_runic_obstruction
    save_config()  # Save the toggle state to config

def filter_and_format_message(message):
    """Filter the message, extract details, and format the output."""
    if not show_runic_obstruction:
        # If Runic Obstruction messages are disabled, skip them
        if pattern_1.search(message) or pattern_2.search(message):
            return None

    match_1 = pattern_1.search(message)
    match_2 = pattern_2.search(message)
    match_3 = pattern_3.search(message)
    
    if match_1:
        name, time_duration = match_1.groups()
        time_duration = int(time_duration)  # Convert time to integer
        # Format the message: (name) is RUNICED for (time) seconds with "RUNICED" in green
        return (f"{name} is RUNICED for {time_duration} seconds", 0x00ff00, time_duration)

    elif match_2:
        name, time_duration = match_2.groups()
        time_duration = int(time_duration)  # Convert time to integer
        # Format the message: You are RUNICED for (time) seconds with "RUNICED" in red
        return (f"You are RUNICED for {time_duration} seconds", 0xff0000, time_duration)
    
    elif match_3:
        name = match_3.group(1)
        # Format the new message: "Anc helm procced on (name) for 7 seconds"
        return (f"Anc helm procced on {name} for 7 seconds", 0x00ff00, 7)  # Green color

    return None

def remove_duplicate_message(message):
    """Remove an old duplicate message from chat_messages and chat_text_objects."""
    global chat_messages, chat_text_objects

    # Extract the base part of the message (before "for x seconds") to compare
    base_message = message.split('for')[0]

    # Check if the message with the same base content already exists
    for i, (msg, color, time_left) in enumerate(chat_messages):
        if base_message in msg:
            # Remove the old message and text object
            chat_messages.pop(i)
            chat_text_objects.pop(i)
            break

def display_message(msg, color, time_left):
    """Display a new message immediately on the screen."""
    global chat_text_objects

    # Calculate the position for the new message
    y = new_message_y - (len(chat_messages) - 1) * 5
    scale = 2

    # Draw a new centered message with countdown
    text_obj = draw_centered_string(msg, x=message_x, y=y, color=color, scale=scale)
    chat_text_objects.append(text_obj)
    return text_obj

def on_chat_message(message):
    """Handle chat messages, filter and format them, and update the rendered text."""
    global chat_messages, chat_text_objects
    
    # Process the message, filter and format it
    formatted_message = filter_and_format_message(message)
    if not formatted_message:
        return

    msg, color, time_left = formatted_message

    # Remove any duplicate message
    remove_duplicate_message(msg)

    # Append the new formatted message to the chat message list
    chat_messages.append(formatted_message)

    # Display the message immediately
    display_message(msg, color, time_left)
    
    # If there are more messages than allowed, remove the oldest one
    if len(chat_messages) > 5:
        chat_messages.pop(0)
        chat_text_objects.pop(0)

def update_messages():
    """Update the countdown for each message and remove messages when the timer hits zero."""
    global chat_messages, chat_text_objects, last_update_time

    # Get the current time
    current_time = time.time()

    # Calculate how much time has passed since the last update
    time_elapsed = current_time - last_update_time

    if time_elapsed >= 1:  # Only update the countdown once a full second has passed
        # Update each message's countdown
        for i in range(len(chat_messages) - 1, -1, -1):  # Iterate in reverse order to avoid index issues
            msg, color, time_left = chat_messages[i]

            # Reduce the time left by 1 second
            time_left -= int(time_elapsed)

            if time_left <= 0:
                # Remove the message if time hits zero
                chat_messages.pop(i)
                chat_text_objects.pop(i)
            else:
                # Update the message with the new time
                updated_message = msg.split('for')[0] + f"for {time_left} seconds"
                chat_messages[i] = (updated_message, color, time_left)

                # Recalculate the y-position for each message
                y = new_message_y - (len(chat_messages) - 1 - i) * 5
                scale = 2

                # Update the existing text object
                chat_text_objects[i].string.set_value(updated_message)  # Update the message string
                chat_text_objects[i].y.set_value(y)  # Update the position
                chat_text_objects[i].color.set_value(color)  # Update the color

        # Reset the last update time
        last_update_time = current_time

# Function to create the GUI
def create_gui():
    """Create a Tkinter GUI to adjust values."""
    root = tk.Tk()
    root.title("Minescript Settings")

    # Create labels and sliders for each adjustable parameter
    message_x_label = ttk.Label(root, text="Message X Position:")
    message_x_label.pack()

    message_x_value = tk.Label(root, text=f"{message_x}")
    message_x_value.pack()

    message_x_slider = ttk.Scale(root, from_=0, to=500, orient='horizontal', length=400,
                                 command=lambda value: [update_message_x(value), message_x_value.config(text=f"{int(float(value))}")])
    message_x_slider.set(message_x)
    message_x_slider.pack()

    new_message_y_label = ttk.Label(root, text="Message Y Position:")
    new_message_y_label.pack()

    new_message_y_value = tk.Label(root, text=f"{new_message_y}")
    new_message_y_value.pack()

    new_message_y_slider = ttk.Scale(root, from_=0, to=200, orient='horizontal', length=400,
                                     command=lambda value: [update_new_message_y(value), new_message_y_value.config(text=f"{int(float(value))}")])
    new_message_y_slider.set(new_message_y)
    new_message_y_slider.pack()

    # Add "Toggle Rune Messages" checkbox
    rune_messages_var = tk.BooleanVar(value=show_rune_messages)
    rune_messages_checkbox = ttk.Checkbutton(root, text="Show Rune Messages", variable=rune_messages_var, command=toggle_rune_messages)
    rune_messages_checkbox.pack()

    # Add "Toggle Runic Obstruction Messages" checkbox
    runic_obstruction_var = tk.BooleanVar(value=show_runic_obstruction)
    runic_obstruction_checkbox = ttk.Checkbutton(root, text="Show Runic Obstruction Messages", variable=runic_obstruction_var, command=toggle_runic_obstruction)
    runic_obstruction_checkbox.pack()

    # Add "Show Test Text" toggle button
    test_button = ttk.Button(root, text="Toggle Test Text", command=toggle_test_message)
    test_button.pack()

    root.mainloop()

# Function to run the GUI in a separate thread
def run_gui():
    """Run the GUI in a separate thread."""
    gui_thread = threading.Thread(target=create_gui)
    gui_thread.daemon = True
    gui_thread.start()

# Main function
def main():
    """Continuously monitor chat for updates, update the countdown, and display the filtered messages."""
    read_config()  # Read config at startup
    run_gui()  # Start the GUI in a separate thread

    with EventQueue() as event_queue:
        event_queue.register_chat_listener()
        while True:
            try:
                event = event_queue.get(timeout=0.1)
                if event and event.type == EventType.CHAT:
                    on_chat_message(event.message)
            except queue.Empty:
                pass
            except Exception as e:
                print(f"Error in event loop: {e}")
            update_messages()  # Call the missing update_messages function to handle countdown

if __name__ == "__main__":
    main()
